# Database Design Document

## 1. Executive Summary

This document outlines the database design for the Plover Project Management Tool. The design is based on the functional and non-functional requirements of the system, as well as the system architecture. The database will be used to store information about tasks, boards, payments, subscriptions, and users. PostgreSQL has been selected as the primary database system due to its robustness, support for complex queries, and ACID transactions.

## 2. Requirements Analysis

### 2.1 Data Requirements

- Key entities: Task, Board, Payment, Subscription, User
- Attributes:
    - Task: id, title, description, assignee, deadline, status
    - Board: id, name, description, members
    - Payment: id, user_id, amount, status
    - Subscription: id, user_id, plan_id, start_date, end_date
    - User: id, username, roles, access_token, created_at, updated_at
- Primary relationships:
    - Task: One-to-many relationship with User (assignee)
    - Board: Many-to-many relationship with User (members)
    - Payment: One-to-one relationship with User
    - Subscription: One-to-one relationship with User

### 2.2 Functional Requirements

- Core application functions requiring database support:
    - Task management
    - Team collaboration
    - Payment processing
    - Subscription management
    - User authentication and authorization
- Key queries and data access patterns:
    - Get all tasks assigned to a user
    - Get all boards a user is a member of
    - Get all payments made by a user
    - Get all subscriptions for a user

### 2.3 Non-Functional Requirements

- Performance expectations: The database should be able to handle a large number of concurrent users and transactions.
- Scalability needs: The database should be able to scale horizontally to accommodate a growing number of users and data.
- Security and compliance requirements: The database should be secure and compliant with relevant regulations.

## 3. Database Selection

### 3.1 Selected Database Technology

- Primary database system: PostgreSQL
- Justification: PostgreSQL is a robust, open-source relational database with excellent support for complex queries, JSON, geographic data, and reliability.

### 3.2 Comparison of Alternatives

| Database    | Pros                                                                 | Cons                                                                 | Fit for Requirements |
|-------------|----------------------------------------------------------------------|----------------------------------------------------------------------|----------------------|
| PostgreSQL  | Robust, open-source, complex queries, JSON, geographic data, reliable | Can be complex to set up and manage                                  | Excellent            |
| MySQL       | Popular, open-source, good performance                               | Lacks some advanced features of PostgreSQL                           | Good                 |
| SQLite      | Lightweight, file-based                                              | Not suitable for high-volume, concurrent access                       | Poor                 |
| MongoDB     | Flexible schema, rapid prototyping                                   | Not suitable for complex queries or ACID transactions                 | Poor                 |

## 4. Logical Data Model

### 4.1 Entity-Relationship Diagram

[Detailed ERD diagram with entities, relationships, attributes, and cardinality. Include both textual description and diagram representation]

### 4.2 PlantUML Diagram Code

@startuml database-diagram
entity "User" as User {
  id SERIAL PRIMARY KEY,
    uid VARCHAR(255) UNIQUE NOT NULL,     -- Pi Network user ID
    username VARCHAR(255) NOT NULL,       -- Pi Network username
    roles JSON,                           -- User roles array
    access_token TEXT,                    -- Current Pi access token
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
}

entity "Task" as Task {
  *id : INT <<PK>>
  --
  title : VARCHAR(255)
  description : TEXT
  assignee_id : INT <<FK>>
  deadline : DATETIME
  status : VARCHAR(255)
}

entity "Board" as Board {
  *id : INT <<PK>>
  --
  name : VARCHAR(255)
  description : TEXT
}

entity "Board_Members" as Board_Members {
  *board_id : INT <<FK>>
  *user_id : INT <<FK>>
}

entity "Payment" as Payment {
  *id : INT <<PK>>
  --
  user_id : INT <<FK>>
  amount : DECIMAL
  status : VARCHAR(255)
}

entity "Subscription" as Subscription {
  *id : INT <<PK>>
  --
  user_id : INT <<FK>>
  plan_id : VARCHAR(255)
  start_date : DATETIME
  end_date : DATETIME
}

User ||--o{ Task : assigns
User ||--o{ Payment : makes
User ||--o{ Subscription : subscribes
Board ||--o{ Board_Members : has
User ||--o{ Board_Members : participates

Task }|--|| User : assigned to
Payment }|--|| User : made by
Subscription }|--|| User : subscribed by
Board_Members }|--|| Board : member of
Board_Members }|--|| User : member

@enduml

To generate the diagram image, run:
plantuml database-diagram.puml

### 4.3 Entity Definitions

| Entity       | Description                                                                 | Key Attributes | Relationships