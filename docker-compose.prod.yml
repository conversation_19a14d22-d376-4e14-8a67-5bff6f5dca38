version: '3.8'

services:
  postgres:
    image: postgres:15
    container_name: plover-postgres-prod
    environment:
      POSTGRES_DB: plover_pm
      POSTGRES_USER: plover_user
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-secure_password_change_this}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - plover-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U plover_user -d plover_pm"]
      interval: 30s
      timeout: 10s
      retries: 3

  redis:
    image: redis:7-alpine
    container_name: plover-redis-prod
    volumes:
      - redis_data:/data
    networks:
      - plover-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.prod
    container_name: plover-backend-prod
    env_file:
      - ./backend/.env.production
    environment:
      - DATABASE_URL=postgresql+asyncpg://plover_user:${POSTGRES_PASSWORD:-secure_password_change_this}@postgres:5432/plover_pm
      - REDIS_URL=redis://redis:6379
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - plover-network
    restart: unless-stopped
    volumes:
      - ./backend/logs:/app/logs

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.prod
    container_name: plover-frontend-prod
    env_file:
      - ./frontend/.env.production
    depends_on:
      - backend
    networks:
      - plover-network
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    container_name: plover-nginx-prod
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
      - ./nginx/logs:/var/log/nginx
    depends_on:
      - frontend
      - backend
    networks:
      - plover-network
    restart: unless-stopped

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local

networks:
  plover-network:
    driver: bridge
