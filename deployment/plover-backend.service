[Unit]
Description=Plover Backend API
After=network.target postgresql.service redis.service
Wants=postgresql.service redis.service

[Service]
Type=exec
User=plover
Group=plover
WorkingDirectory=/opt/plover/backend
Environment=PATH=/opt/plover/backend/venv/bin
ExecStart=/opt/plover/backend/venv/bin/uvicorn main:app --host 0.0.0.0 --port 8000 --workers 4
ExecReload=/bin/kill -HUP $MAINPID
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal
SyslogIdentifier=plover-backend

# Security settings
NoNewPrivileges=yes
PrivateTmp=yes
ProtectSystem=strict
ProtectHome=yes
ReadWritePaths=/opt/plover/backend/logs

[Install]
WantedBy=multi-user.target
