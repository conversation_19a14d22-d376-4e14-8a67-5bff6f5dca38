[Unit]
Description=Plover Frontend
After=network.target plover-backend.service
Wants=plover-backend.service

[Service]
Type=exec
User=plover
Group=plover
WorkingDirectory=/opt/plover/frontend
Environment=NODE_ENV=production
Environment=PORT=3000
ExecStart=/usr/bin/npm start
ExecReload=/bin/kill -HUP $MAINPID
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal
SyslogIdentifier=plover-frontend

# Security settings
NoNewPrivileges=yes
PrivateTmp=yes
ProtectSystem=strict
ProtectHome=yes

[Install]
WantedBy=multi-user.target
