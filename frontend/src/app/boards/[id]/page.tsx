'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useParams } from 'next/navigation';
import Link from 'next/link';
import { useAuth } from '@/contexts/AuthContext';
import Header from '@/components/layout/Header';
import Button from '@/components/ui/Button';
import Card from '@/components/ui/Card';
import { api } from '@/lib/api';
import {
  ArrowLeftIcon,
  PlusIcon,
  CheckCircleIcon,
  ClockIcon,
  PencilIcon,
} from '@heroicons/react/24/outline';

interface Task {
  id: number;
  title: string;
  status: 'TODO' | 'IN_PROGRESS' | 'COMPLETED';
  priority: 'LOW' | 'MEDIUM' | 'HIGH';
  assignee?: {
    id: number;
    username: string;
  };
}

interface Board {
  id: number;
  name: string;
  description: string;
  is_public: boolean;
  created_at: string;
  tasks: Task[];
  project?: {
    id: number;
    name: string;
  };
}

const BoardDetailPage: React.FC = () => {
  const { isAuthenticated } = useAuth();
  const params = useParams();
  const boardId = params.id as string;
  
  const [board, setBoard] = useState<Board | null>(null);
  const [loading, setLoading] = useState(true);

  const fetchBoard = useCallback(async () => {
    try {
      const response = await api.boards.get(parseInt(boardId));
      setBoard(response.data.board);
    } catch (error) {
      console.error('Failed to fetch board:', error);
    } finally {
      setLoading(false);
    }
  }, [boardId]);

  useEffect(() => {
    if (isAuthenticated && boardId) {
      fetchBoard();
    }
  }, [isAuthenticated, boardId, fetchBoard]);

  const getStatusIcon = (status: Task['status']) => {
    switch (status) {
      case 'COMPLETED':
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
      case 'IN_PROGRESS':
        return <ClockIcon className="h-5 w-5 text-yellow-500" />;
      default:
        return <div className="h-5 w-5 rounded-full border-2 border-gray-300" />;
    }
  };

  const getPriorityColor = (priority: Task['priority']) => {
    switch (priority) {
      case 'HIGH':
        return 'bg-red-100 text-red-800';
      case 'MEDIUM':
        return 'bg-yellow-100 text-yellow-800';
      case 'LOW':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const groupTasksByStatus = (tasks: Task[]) => {
    return {
      TODO: tasks.filter(task => task.status === 'TODO'),
      IN_PROGRESS: tasks.filter(task => task.status === 'IN_PROGRESS'),
      COMPLETED: tasks.filter(task => task.status === 'COMPLETED'),
    };
  };

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-primary-500 mb-4">
            Please sign in to view boards
          </h1>
          <Link href="/">
            <Button variant="primary">Go to Home</Button>
          </Link>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto"></div>
            <p className="text-gray-600 mt-4">Loading board...</p>
          </div>
        </main>
      </div>
    );
  }

  if (!board) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center py-12">
            <h2 className="text-2xl font-bold text-gray-600 mb-4">Board not found</h2>
            <Link href="/boards">
              <Button variant="primary">Back to Boards</Button>
            </Link>
          </div>
        </main>
      </div>
    );
  }

  const taskGroups = groupTasksByStatus(board.tasks);

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center">
            <Link href="/boards">
              <Button variant="ghost" size="sm" className="mr-4">
                <ArrowLeftIcon className="h-4 w-4 mr-2" />
                Back to Boards
              </Button>
            </Link>
            <div>
              <h1 className="text-3xl font-bold text-primary-500">{board.name}</h1>
              <p className="text-gray-600 mt-2">
                {board.project && (
                  <>
                    Project: <Link href={`/projects/${board.project.id}`} className="text-primary-600 hover:text-primary-700">{board.project.name}</Link> • 
                  </>
                )}
                Created {new Date(board.created_at).toLocaleDateString()}
              </p>
            </div>
          </div>
          
          <div className="flex space-x-2">
            <Link href="/tasks/new">
              <Button variant="primary" size="sm">
                <PlusIcon className="h-4 w-4 mr-2" />
                Add Task
              </Button>
            </Link>
            <Button variant="secondary" size="sm">
              <PencilIcon className="h-4 w-4 mr-2" />
              Edit Board
            </Button>
          </div>
        </div>

        {/* Description */}
        {board.description && (
          <Card className="mb-8">
            <p className="text-gray-700">{board.description}</p>
          </Card>
        )}

        {/* Kanban Board */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {Object.entries(taskGroups).map(([status, tasks]) => (
            <div key={status}>
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-lg font-semibold text-primary-500">
                  {status.replace('_', ' ')} ({tasks.length})
                </h2>
              </div>
              
              <div className="space-y-3">
                {tasks.map((task) => (
                  <Link key={task.id} href={`/tasks/${task.id}`}>
                    <Card className="hover:border-primary-300 transition-colors cursor-pointer">
                      <div className="flex items-start justify-between mb-3">
                        <h3 className="font-medium text-gray-900 flex-1">
                          {task.title}
                        </h3>
                        {getStatusIcon(task.status)}
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <span className={`px-2 py-1 text-xs rounded-full ${getPriorityColor(task.priority)}`}>
                          {task.priority}
                        </span>
                        
                        {task.assignee && (
                          <div className="flex items-center text-sm text-gray-600">
                            <div className="w-6 h-6 bg-primary-100 rounded-full flex items-center justify-center mr-2">
                              <span className="text-primary-600 text-xs font-medium">
                                {task.assignee.username.charAt(0).toUpperCase()}
                              </span>
                            </div>
                            <span className="text-xs">{task.assignee.username}</span>
                          </div>
                        )}
                      </div>
                    </Card>
                  </Link>
                ))}
                
                {tasks.length === 0 && (
                  <div className="text-center py-8 text-gray-500">
                    <p className="text-sm">No tasks in this column</p>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      </main>
    </div>
  );
};

export default function BoardDetail() {
  return <BoardDetailPage />;
}
