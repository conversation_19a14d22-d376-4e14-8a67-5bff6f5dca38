'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useAuth } from '@/contexts/AuthContext';
import Header from '@/components/layout/Header';
import Button from '@/components/ui/Button';
import Card from '@/components/ui/Card';
import { api } from '@/lib/api';
import {
  UserCircleIcon,
  CalendarIcon,
  CheckCircleIcon,
  ClipboardDocumentListIcon,
  FolderIcon,
  Cog6ToothIcon,
} from '@heroicons/react/24/outline';

interface UserStats {
  tasks_completed: number;
  tasks_in_progress: number;
  boards_created: number;
  projects_owned: number;
  team_members: number;
}

const ProfilePage: React.FC = () => {
  const { isAuthenticated, user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState<UserStats>({
    tasks_completed: 0,
    tasks_in_progress: 0,
    boards_created: 0,
    projects_owned: 0,
    team_members: 0,
  });

  useEffect(() => {
    if (isAuthenticated) {
      fetchUserStats();
    }
  }, [isAuthenticated]);

  const fetchUserStats = async () => {
    try {
      const response = await api.users.stats();
      setStats(response.data);
    } catch (error) {
      console.error('Failed to fetch user stats:', error);
      // Set default values on error
      setStats({
        tasks_completed: 0,
        tasks_in_progress: 0,
        boards_created: 0,
        projects_owned: 0,
        team_members: 0,
      });
    } finally {
      setLoading(false);
    }
  };

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-primary-500 mb-4">
            Please sign in to view your profile
          </h1>
          <Link href="/">
            <Button variant="primary">Go to Home</Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-primary-500">Profile</h1>
            <p className="text-gray-600 mt-2">
              Your account information and activity
            </p>
          </div>
          <Link href="/settings">
            <Button variant="secondary">
              <Cog6ToothIcon className="h-5 w-5 mr-2" />
              Settings
            </Button>
          </Link>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Profile Info */}
          <div className="lg:col-span-1">
            <Card>
              <div className="text-center">
                <div className="w-24 h-24 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <UserCircleIcon className="h-16 w-16 text-primary-500" />
                </div>
                
                <h2 className="text-2xl font-bold text-primary-500 mb-2">
                  {user?.username || 'User'}
                </h2>
                
                <p className="text-gray-600 mb-4">
                  {user?.email || 'No email provided'}
                </p>
                
                <div className="flex items-center justify-center text-sm text-gray-500 mb-6">
                  <CalendarIcon className="h-4 w-4 mr-2" />
                  Joined January 2024
                </div>
                
                <div className="space-y-2">
                  <Link href="/settings">
                    <Button variant="primary" size="sm" className="w-full">
                      Edit Profile
                    </Button>
                  </Link>
                </div>
              </div>
            </Card>
          </div>

          {/* Stats and Activity */}
          <div className="lg:col-span-2 space-y-6">
            {/* Stats Cards */}
            <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
              <Card className="text-center">
                <CheckCircleIcon className="h-8 w-8 text-green-500 mx-auto mb-2" />
                <div className="text-2xl font-bold text-primary-500">
                  {loading ? '...' : stats.tasks_completed}
                </div>
                <div className="text-sm text-gray-600">Tasks Completed</div>
              </Card>
              
              <Card className="text-center">
                <ClipboardDocumentListIcon className="h-8 w-8 text-yellow-500 mx-auto mb-2" />
                <div className="text-2xl font-bold text-primary-500">
                  {loading ? '...' : stats.tasks_in_progress}
                </div>
                <div className="text-sm text-gray-600">In Progress</div>
              </Card>
              
              <Card className="text-center">
                <FolderIcon className="h-8 w-8 text-blue-500 mx-auto mb-2" />
                <div className="text-2xl font-bold text-primary-500">
                  {loading ? '...' : stats.boards_created}
                </div>
                <div className="text-sm text-gray-600">Boards Created</div>
              </Card>
              
              <Card className="text-center">
                <FolderIcon className="h-8 w-8 text-purple-500 mx-auto mb-2" />
                <div className="text-2xl font-bold text-primary-500">
                  {loading ? '...' : stats.projects_owned}
                </div>
                <div className="text-sm text-gray-600">Projects Owned</div>
              </Card>
              
              <Card className="text-center">
                <UserCircleIcon className="h-8 w-8 text-indigo-500 mx-auto mb-2" />
                <div className="text-2xl font-bold text-primary-500">
                  {loading ? '...' : stats.team_members}
                </div>
                <div className="text-sm text-gray-600">Team Members</div>
              </Card>
            </div>

            {/* Recent Activity */}
            <Card>
              <h3 className="text-lg font-semibold text-primary-500 mb-4">
                Recent Activity
              </h3>
              
              {loading ? (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500 mx-auto"></div>
                  <p className="text-gray-600 mt-2">Loading activity...</p>
                </div>
              ) : (
                <div className="space-y-4">
                  <div className="flex items-center space-x-3 text-sm">
                    <CheckCircleIcon className="h-5 w-5 text-green-500" />
                    <span className="text-gray-600">Completed task &quot;Update documentation&quot;</span>
                    <span className="text-gray-400">2 hours ago</span>
                  </div>

                  <div className="flex items-center space-x-3 text-sm">
                    <ClipboardDocumentListIcon className="h-5 w-5 text-blue-500" />
                    <span className="text-gray-600">Created board &quot;Sprint Planning&quot;</span>
                    <span className="text-gray-400">1 day ago</span>
                  </div>
                  
                  <div className="flex items-center space-x-3 text-sm">
                    <UserCircleIcon className="h-5 w-5 text-purple-500" />
                    <span className="text-gray-600">Invited 2 team members</span>
                    <span className="text-gray-400">3 days ago</span>
                  </div>
                </div>
              )}
            </Card>
          </div>
        </div>
      </main>
    </div>
  );
};

export default function Profile() {
  return <ProfilePage />;
}
