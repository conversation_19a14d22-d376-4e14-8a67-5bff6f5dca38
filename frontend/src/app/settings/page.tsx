'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { useAuth } from '@/contexts/AuthContext';
import Header from '@/components/layout/Header';
import Button from '@/components/ui/Button';
import Card from '@/components/ui/Card';
import Input from '@/components/ui/Input';

import {
  UserCircleIcon,
  BellIcon,
  ShieldCheckIcon,
} from '@heroicons/react/24/outline';

const SettingsPage: React.FC = () => {
  const { isAuthenticated, user } = useAuth();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    username: user?.username || '',
    email: user?.email || '',
    notifications: {
      email: true,
      push: true,
      mentions: true,
    },
  });

  const handleSave = async () => {
    setLoading(true);
    try {
      // TODO: Replace with actual API call
      console.log('Saving settings:', formData);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      alert('Settings saved successfully!');
    } catch (error) {
      console.error('Failed to save settings:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: string, value: string | boolean) => {
    if (field.includes('.')) {
      const [parent, child] = field.split('.');
      setFormData(prev => ({
        ...prev,
        [parent]: {
          ...prev[parent as keyof typeof prev] as any,
          [child]: value,
        },
      }));
    } else {
      setFormData(prev => ({ ...prev, [field]: value }));
    }
  };

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-primary-500 mb-4">
            Please sign in to access settings
          </h1>
          <Link href="/">
            <Button variant="primary">Go to Home</Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-primary-500">Settings</h1>
          <p className="text-gray-600 mt-2">
            Manage your account preferences and settings
          </p>
        </div>

        <div className="space-y-6">
          {/* Profile Settings */}
          <Card>
            <div className="flex items-center mb-6">
              <UserCircleIcon className="h-6 w-6 text-primary-500 mr-3" />
              <h2 className="text-xl font-semibold text-primary-500">Profile</h2>
            </div>
            
            <div className="space-y-4">
              <div>
                <label htmlFor="username" className="block text-sm font-medium text-gray-700 mb-2">
                  Username
                </label>
                <Input
                  id="username"
                  type="text"
                  value={formData.username}
                  onChange={(e) => handleInputChange('username', e.target.value)}
                />
              </div>
              
              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                  Email
                </label>
                <Input
                  id="email"
                  type="email"
                  value={formData.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                />
              </div>
            </div>
          </Card>



          {/* Notification Settings */}
          <Card>
            <div className="flex items-center mb-6">
              <BellIcon className="h-6 w-6 text-primary-500 mr-3" />
              <h2 className="text-xl font-semibold text-primary-500">Notifications</h2>
            </div>
            
            <div className="space-y-4">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                  checked={formData.notifications.email}
                  onChange={(e) => handleInputChange('notifications.email', e.target.checked)}
                />
                <span className="ml-3 text-sm text-gray-700">
                  Email notifications
                </span>
              </label>
              
              <label className="flex items-center">
                <input
                  type="checkbox"
                  className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                  checked={formData.notifications.push}
                  onChange={(e) => handleInputChange('notifications.push', e.target.checked)}
                />
                <span className="ml-3 text-sm text-gray-700">
                  Push notifications
                </span>
              </label>
              
              <label className="flex items-center">
                <input
                  type="checkbox"
                  className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                  checked={formData.notifications.mentions}
                  onChange={(e) => handleInputChange('notifications.mentions', e.target.checked)}
                />
                <span className="ml-3 text-sm text-gray-700">
                  Mention notifications
                </span>
              </label>
            </div>
          </Card>

          {/* Security Settings */}
          <Card>
            <div className="flex items-center mb-6">
              <ShieldCheckIcon className="h-6 w-6 text-primary-500 mr-3" />
              <h2 className="text-xl font-semibold text-primary-500">Security</h2>
            </div>
            
            <div className="space-y-4">
              <p className="text-sm text-gray-600">
                Your account is secured with Pi Network authentication.
              </p>
              <Button variant="secondary" size="sm">
                Manage Pi Network Settings
              </Button>
            </div>
          </Card>

          {/* Save Button */}
          <div className="flex justify-end">
            <Button 
              variant="primary" 
              onClick={handleSave}
              loading={loading}
            >
              Save Changes
            </Button>
          </div>
        </div>
      </main>
    </div>
  );
};

export default function Settings() {
  return <SettingsPage />;
}
