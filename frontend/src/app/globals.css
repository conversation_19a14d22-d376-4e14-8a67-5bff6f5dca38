@import "tailwindcss";

@theme {
  --color-primary-50: #f0f9f8;
  --color-primary-100: #ccebe8;
  --color-primary-200: #99d6d0;
  --color-primary-300: #66c2b8;
  --color-primary-400: #33ada0;
  --color-primary-500: #074944;
  --color-primary-600: #063a37;
  --color-primary-700: #052c2a;
  --color-primary-800: #041d1c;
  --color-primary-900: #020f0e;

  --color-accent-50: #fefbf0;
  --color-accent-100: #fdf4cc;
  --color-accent-200: #fbe999;
  --color-accent-300: #f9de66;
  --color-accent-400: #f7d333;
  --color-accent-500: #ECC41C;
  --color-accent-600: #bd9d16;
  --color-accent-700: #8e7611;
  --color-accent-800: #5f4e0b;
  --color-accent-900: #2f2706;
}

:root {
  --background: #ffffff;
  --foreground: #074944;
}

body {
  color: var(--foreground);
  background: var(--background);
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

@layer base {
  html {
    @apply scroll-smooth;
  }

  body {
    @apply bg-gray-50 text-primary-500;
  }
}

@layer components {
  .btn-primary {
    @apply bg-primary-500 hover:bg-primary-600 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200;
  }

  .btn-secondary {
    @apply bg-white hover:bg-gray-50 text-primary-500 font-medium py-2 px-4 rounded-lg border border-primary-200 transition-colors duration-200;
  }

  .btn-accent {
    @apply bg-accent-500 hover:bg-accent-600 text-primary-900 font-medium py-2 px-4 rounded-lg transition-colors duration-200;
  }

  .card {
    @apply bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6;
  }

  /* Dark mode styles */
  .dark {
    color-scheme: dark;
  }

  .dark body {
    @apply bg-gray-900 text-gray-100;
  }

  .dark .bg-gray-50 {
    @apply bg-gray-900;
  }

  .dark .bg-white {
    @apply bg-gray-800;
  }

  .dark .text-gray-900 {
    @apply text-gray-100;
  }

  .dark .text-gray-800 {
    @apply text-gray-200;
  }

  .dark .text-gray-700 {
    @apply text-gray-300;
  }

  .dark .text-gray-600 {
    @apply text-gray-400;
  }

  .dark .text-gray-500 {
    @apply text-gray-500;
  }

  .dark .border-gray-200 {
    @apply border-gray-700;
  }

  .dark .border-gray-300 {
    @apply border-gray-600;
  }

  .input-field {
    @apply w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent;
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}
