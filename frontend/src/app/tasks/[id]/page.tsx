'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import Link from 'next/link';
import { useAuth } from '@/contexts/AuthContext';
import Header from '@/components/layout/Header';
import Button from '@/components/ui/Button';
import Card from '@/components/ui/Card';
import { api } from '@/lib/api';
import {
  ArrowLeftIcon,
  CheckCircleIcon,
  ClockIcon,
  PencilIcon,
  TrashIcon,
} from '@heroicons/react/24/outline';

interface Task {
  id: number;
  title: string;
  description: string;
  status: 'TODO' | 'IN_PROGRESS' | 'COMPLETED';
  priority: 'LOW' | 'MEDIUM' | 'HIGH';
  due_date: string | null;
  created_at: string;
  updated_at: string;
  assignee?: {
    id: number;
    username: string;
  };
  board?: {
    id: number;
    name: string;
  };
}

const TaskDetailPage: React.FC = () => {
  const { isAuthenticated } = useAuth();
  const params = useParams();
  const router = useRouter();
  const taskId = params.id as string;
  
  const [task, setTask] = useState<Task | null>(null);
  const [loading, setLoading] = useState(true);
  const [updating, setUpdating] = useState(false);

  const fetchTask = useCallback(async () => {
    try {
      const response = await api.tasks.get(parseInt(taskId));
      setTask(response.data.task);
    } catch (error) {
      console.error('Failed to fetch task:', error);
    } finally {
      setLoading(false);
    }
  }, [taskId]);

  useEffect(() => {
    if (isAuthenticated && taskId) {
      fetchTask();
    }
  }, [isAuthenticated, taskId, fetchTask]);

  const handleStatusChange = async (newStatus: Task['status']) => {
    if (!task) return;

    setUpdating(true);
    try {
      await api.tasks.update(task.id, { status: newStatus });
      setTask(prev => prev ? { ...prev, status: newStatus } : null);
    } catch (error) {
      console.error('Failed to update task status:', error);
    } finally {
      setUpdating(false);
    }
  };

  const handleDelete = async () => {
    if (!task || !confirm('Are you sure you want to delete this task?')) return;

    setUpdating(true);
    try {
      await api.tasks.delete(task.id);
      router.push('/tasks');
    } catch (error) {
      console.error('Failed to delete task:', error);
      setUpdating(false);
    }
  };

  const getStatusIcon = (status: Task['status']) => {
    switch (status) {
      case 'COMPLETED':
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
      case 'IN_PROGRESS':
        return <ClockIcon className="h-5 w-5 text-yellow-500" />;
      default:
        return <div className="h-5 w-5 rounded-full border-2 border-gray-300" />;
    }
  };

  const getPriorityColor = (priority: Task['priority']) => {
    switch (priority) {
      case 'HIGH':
        return 'bg-red-100 text-red-800';
      case 'MEDIUM':
        return 'bg-yellow-100 text-yellow-800';
      case 'LOW':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-primary-500 mb-4">
            Please sign in to view tasks
          </h1>
          <Link href="/">
            <Button variant="primary">Go to Home</Button>
          </Link>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto"></div>
            <p className="text-gray-600 mt-4">Loading task...</p>
          </div>
        </main>
      </div>
    );
  }

  if (!task) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center py-12">
            <h2 className="text-2xl font-bold text-gray-600 mb-4">Task not found</h2>
            <Link href="/tasks">
              <Button variant="primary">Back to Tasks</Button>
            </Link>
          </div>
        </main>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center">
            <Link href="/tasks">
              <Button variant="ghost" size="sm" className="mr-4">
                <ArrowLeftIcon className="h-4 w-4 mr-2" />
                Back to Tasks
              </Button>
            </Link>
            <div>
              <h1 className="text-3xl font-bold text-primary-500">{task.title}</h1>
              <p className="text-gray-600 mt-2">
                Task #{task.id} • Created {new Date(task.created_at).toLocaleDateString()}
              </p>
            </div>
          </div>
          
          <div className="flex space-x-2">
            <Button variant="secondary" size="sm">
              <PencilIcon className="h-4 w-4 mr-2" />
              Edit
            </Button>
            <Button 
              variant="secondary" 
              size="sm" 
              onClick={handleDelete}
              loading={updating}
              className="text-red-600 hover:text-red-700"
            >
              <TrashIcon className="h-4 w-4 mr-2" />
              Delete
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Content */}
          <div className="lg:col-span-2">
            <Card>
              <h2 className="text-lg font-semibold text-primary-500 mb-4">Description</h2>
              <p className="text-gray-700 whitespace-pre-wrap">
                {task.description || 'No description provided.'}
              </p>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Status */}
            <Card>
              <h3 className="text-lg font-semibold text-primary-500 mb-4">Status</h3>
              <div className="space-y-2">
                {(['TODO', 'IN_PROGRESS', 'COMPLETED'] as const).map((status) => (
                  <button
                    key={status}
                    onClick={() => handleStatusChange(status)}
                    disabled={updating}
                    className={`w-full flex items-center p-3 rounded-lg border transition-colors ${
                      task.status === status
                        ? 'border-primary-300 bg-primary-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    {getStatusIcon(status)}
                    <span className="ml-3 font-medium">
                      {status.replace('_', ' ')}
                    </span>
                  </button>
                ))}
              </div>
            </Card>

            {/* Details */}
            <Card>
              <h3 className="text-lg font-semibold text-primary-500 mb-4">Details</h3>
              <div className="space-y-4">
                <div>
                  <label className="text-sm font-medium text-gray-600">Priority</label>
                  <div className="mt-1">
                    <span className={`px-3 py-1 text-sm rounded-full ${getPriorityColor(task.priority)}`}>
                      {task.priority}
                    </span>
                  </div>
                </div>
                
                {task.due_date && (
                  <div>
                    <label className="text-sm font-medium text-gray-600">Due Date</label>
                    <div className="mt-1 text-sm text-gray-900">
                      {new Date(task.due_date).toLocaleDateString()}
                    </div>
                  </div>
                )}
                
                {task.assignee && (
                  <div>
                    <label className="text-sm font-medium text-gray-600">Assignee</label>
                    <div className="mt-1 text-sm text-gray-900">
                      {task.assignee.username}
                    </div>
                  </div>
                )}
                
                {task.board && (
                  <div>
                    <label className="text-sm font-medium text-gray-600">Board</label>
                    <div className="mt-1">
                      <Link 
                        href={`/boards/${task.board.id}`}
                        className="text-sm text-primary-600 hover:text-primary-700"
                      >
                        {task.board.name}
                      </Link>
                    </div>
                  </div>
                )}
              </div>
            </Card>
          </div>
        </div>
      </main>
    </div>
  );
};

export default function TaskDetail() {
  return <TaskDetailPage />;
}
