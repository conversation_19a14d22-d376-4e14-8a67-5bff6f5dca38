'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useAuth } from '@/contexts/AuthContext';
import AuthProvider from '@/contexts/AuthContext';
import Header from '@/components/layout/Header';
import Button from '@/components/ui/Button';
import Card from '@/components/ui/Card';
import Input from '@/components/ui/Input';
import { api } from '@/lib/api';
import emailjs from '@emailjs/browser';
import {
  QuestionMarkCircleIcon,
  ChatBubbleLeftRightIcon,
  DocumentTextIcon,
  EnvelopeIcon,
  CheckCircleIcon,
  ClockIcon,
  ExclamationTriangleIcon,
} from '@heroicons/react/24/outline';

interface SupportTicket {
  id: string;
  ticket_number: string;
  subject: string;
  message: string;
  category: string;
  status: 'OPEN' | 'IN_PROGRESS' | 'RESOLVED' | 'CLOSED';
  priority: 'LOW' | 'MEDIUM' | 'HIGH';
  created_at: string;
  updated_at: string;
}

const SupportPage: React.FC = () => {
  const { isAuthenticated } = useAuth();
  const [loading, setLoading] = useState(false);
  const [submitted, setSubmitted] = useState(false);
  const [ticketNumber, setTicketNumber] = useState('');
  const [tickets, setTickets] = useState<SupportTicket[]>([]);
  const [ticketsLoading, setTicketsLoading] = useState(false);
  const [formData, setFormData] = useState({
    subject: '',
    message: '',
    category: 'general',
  });

  useEffect(() => {
    if (isAuthenticated) {
      fetchUserTickets();
    }
  }, [isAuthenticated]);

  const fetchUserTickets = async () => {
    setTicketsLoading(true);
    try {
      const response = await api.support.list();
      setTickets(response.data.tickets || []);
    } catch (error) {
      console.error('Failed to fetch tickets:', error);
    } finally {
      setTicketsLoading(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'OPEN':
        return <ClockIcon className="h-4 w-4 text-yellow-500" />;
      case 'IN_PROGRESS':
        return <ExclamationTriangleIcon className="h-4 w-4 text-blue-500" />;
      case 'RESOLVED':
        return <CheckCircleIcon className="h-4 w-4 text-green-500" />;
      case 'CLOSED':
        return <CheckCircleIcon className="h-4 w-4 text-gray-500" />;
      default:
        return <ClockIcon className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'OPEN':
        return 'bg-yellow-100 text-yellow-800';
      case 'IN_PROGRESS':
        return 'bg-blue-100 text-blue-800';
      case 'RESOLVED':
        return 'bg-green-100 text-green-800';
      case 'CLOSED':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'HIGH':
        return 'bg-red-100 text-red-800';
      case 'MEDIUM':
        return 'bg-yellow-100 text-yellow-800';
      case 'LOW':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.subject.trim() || !formData.message.trim()) return;

    setLoading(true);
    try {
      // Generate ticket number
      const ticketNum = `PLV-${Date.now().toString().slice(-6)}`;

      // Send email via EmailJS
      const templateParams = {
        to_email: '<EMAIL>',
        subject: `[${ticketNum}] ${formData.subject}`,
        message: formData.message,
        category: formData.category,
        ticket_number: ticketNum,
        timestamp: new Date().toISOString(),
      };

      await emailjs.send(
        process.env.NEXT_PUBLIC_EMAILJS_SERVICE_ID || 'your_service_id',
        process.env.NEXT_PUBLIC_EMAILJS_TEMPLATE_ID || 'your_template_id',
        templateParams,
        process.env.NEXT_PUBLIC_EMAILJS_PUBLIC_KEY || 'your_public_key'
      );

      // Save to database via API
      await api.support.create({
        ...formData,
        ticket_number: ticketNum,
        status: 'OPEN',
        priority: formData.category === 'technical' ? 'HIGH' : 'MEDIUM'
      });

      setTicketNumber(ticketNum);
      setSubmitted(true);
      setFormData({ subject: '', message: '', category: 'general' });

      // Refresh tickets list
      if (isAuthenticated) {
        fetchUserTickets();
      }
    } catch (error) {
      console.error('Failed to submit support request:', error);
      alert('Failed to submit support request. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const faqItems = [
    {
      question: "How do I create a new project?",
      answer: "Navigate to the Projects page and click the 'New Project' button. Fill in the project details and click 'Create Project'."
    },
    {
      question: "How do I invite team members?",
      answer: "Go to the Team page and click 'Invite Member'. Enter their Pi Network username and send the invitation."
    },
    {
      question: "Can I use this without Pi Network?",
      answer: "No, this application requires Pi Network authentication for security and blockchain integration."
    },
    {
      question: "How do I change my theme?",
      answer: "Click on the theme toggle in the header to switch between light, dark, and system themes."
    },
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-3xl font-bold text-primary-500 mb-4">Support Center</h1>
          <p className="text-gray-600 text-lg">
            Get help with Plover Project Management Tool
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Contact Form */}
          <div>
            {submitted ? (
              <Card>
                <div className="text-center py-8">
                  <CheckCircleIcon className="h-16 w-16 text-green-500 mx-auto mb-4" />
                  <h2 className="text-2xl font-semibold text-green-600 mb-2">
                    Support Request Submitted!
                  </h2>
                  <p className="text-gray-600 mb-4">
                    Your support request has been submitted successfully.
                  </p>
                  <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
                    <p className="text-sm text-green-800">
                      <strong>Ticket Number:</strong> {ticketNumber}
                    </p>
                    <p className="text-sm text-green-700 mt-2">
                      Please save this ticket number for your records. We&apos;ll get back to you within 24 hours.
                    </p>
                  </div>
                  <Button
                    variant="primary"
                    onClick={() => {
                      setSubmitted(false);
                      setTicketNumber('');
                    }}
                  >
                    Submit Another Request
                  </Button>
                </div>
              </Card>
            ) : (
              <Card>
              <div className="flex items-center mb-6">
                <ChatBubbleLeftRightIcon className="h-6 w-6 text-primary-500 mr-3" />
                <h2 className="text-xl font-semibold text-primary-500">Contact Support</h2>
              </div>

              <form onSubmit={handleSubmit} className="space-y-4">
                <div>
                  <label htmlFor="category" className="block text-sm font-medium text-gray-700 mb-2">
                    Category
                  </label>
                  <select
                    id="category"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                    value={formData.category}
                    onChange={(e) => handleInputChange('category', e.target.value)}
                  >
                    <option value="general">General Question</option>
                    <option value="technical">Technical Issue</option>
                    <option value="billing">Billing</option>
                    <option value="feature">Feature Request</option>
                    <option value="bug">Bug Report</option>
                  </select>
                </div>

                <div>
                  <label htmlFor="subject" className="block text-sm font-medium text-gray-700 mb-2">
                    Subject *
                  </label>
                  <Input
                    id="subject"
                    type="text"
                    placeholder="Brief description of your issue..."
                    value={formData.subject}
                    onChange={(e) => handleInputChange('subject', e.target.value)}
                    required
                  />
                </div>

                <div>
                  <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-2">
                    Message *
                  </label>
                  <textarea
                    id="message"
                    rows={6}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                    placeholder="Please provide as much detail as possible..."
                    value={formData.message}
                    onChange={(e) => handleInputChange('message', e.target.value)}
                    required
                  />
                </div>

                <Button 
                  type="submit" 
                  variant="primary" 
                  loading={loading}
                  disabled={!formData.subject.trim() || !formData.message.trim()}
                  className="w-full"
                >
                  <EnvelopeIcon className="h-5 w-5 mr-2" />
                  Send Message
                </Button>
              </form>
            </Card>
            )}
          </div>

          {/* FAQ and Resources */}
          <div className="space-y-6">
            {/* FAQ */}
            <Card>
              <div className="flex items-center mb-6">
                <QuestionMarkCircleIcon className="h-6 w-6 text-primary-500 mr-3" />
                <h2 className="text-xl font-semibold text-primary-500">Frequently Asked Questions</h2>
              </div>

              <div className="space-y-4">
                {faqItems.map((item, index) => (
                  <div key={index} className="border-b border-gray-200 pb-4 last:border-b-0">
                    <h3 className="font-medium text-gray-900 mb-2">
                      {item.question}
                    </h3>
                    <p className="text-sm text-gray-600">
                      {item.answer}
                    </p>
                  </div>
                ))}
              </div>
            </Card>

            {/* Resources */}
            <Card>
              <div className="flex items-center mb-6">
                <DocumentTextIcon className="h-6 w-6 text-primary-500 mr-3" />
                <h2 className="text-xl font-semibold text-primary-500">Resources</h2>
              </div>

              <div className="space-y-3">
                <Link href="/guide" className="block p-3 rounded-lg border border-gray-200 hover:border-primary-300 transition-colors">
                  <div className="font-medium text-gray-900">User Guide</div>
                  <div className="text-sm text-gray-600">Interactive guide to get started with Plover</div>
                </Link>

                <Link href="/docs" className="block p-3 rounded-lg border border-gray-200 hover:border-primary-300 transition-colors">
                  <div className="font-medium text-gray-900">Documentation</div>
                  <div className="text-sm text-gray-600">Complete guide to using Plover</div>
                </Link>
              </div>
            </Card>

            {/* Contact Info */}
            <Card>
              <h3 className="font-semibold text-primary-500 mb-4">Other Ways to Reach Us</h3>
              <div className="space-y-2 text-sm text-gray-600">
                <p>Email: <EMAIL></p>
                <p>Response time: Usually within 24 hours</p>
                <p>Available: Monday - Friday, 9 AM - 5 PM UTC</p>
              </div>
            </Card>
          </div>
        </div>

        {/* User's Tickets Section */}
        {isAuthenticated && (
          <div className="mt-12">
            <Card>
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center">
                  <ChatBubbleLeftRightIcon className="h-6 w-6 text-primary-500 mr-3" />
                  <h2 className="text-xl font-semibold text-primary-500">Your Support Tickets</h2>
                </div>
                <Button
                  variant="secondary"
                  size="sm"
                  onClick={fetchUserTickets}
                  loading={ticketsLoading}
                >
                  Refresh
                </Button>
              </div>

              {ticketsLoading ? (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500 mx-auto"></div>
                  <p className="text-gray-600 mt-2">Loading tickets...</p>
                </div>
              ) : tickets.length === 0 ? (
                <div className="text-center py-8">
                  <ChatBubbleLeftRightIcon className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                  <p className="text-gray-500">No support tickets yet</p>
                  <p className="text-sm text-gray-400 mt-1">
                    Submit a support request above to get started
                  </p>
                </div>
              ) : (
                <div className="space-y-4">
                  {tickets.map((ticket) => (
                    <div
                      key={ticket.id}
                      className="border border-gray-200 rounded-lg p-4 hover:border-primary-300 transition-colors"
                    >
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-2">
                            <h3 className="font-semibold text-gray-900">
                              {ticket.subject}
                            </h3>
                            <span className="text-sm text-gray-500">
                              #{ticket.ticket_number}
                            </span>
                          </div>
                          <p className="text-sm text-gray-600 line-clamp-2">
                            {ticket.message}
                          </p>
                        </div>
                        <div className="flex flex-col items-end space-y-2 ml-4">
                          <div className="flex items-center space-x-2">
                            {getStatusIcon(ticket.status)}
                            <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(ticket.status)}`}>
                              {ticket.status.replace('_', ' ')}
                            </span>
                          </div>
                          <span className={`px-2 py-1 text-xs rounded-full ${getPriorityColor(ticket.priority)}`}>
                            {ticket.priority}
                          </span>
                        </div>
                      </div>
                      <div className="flex items-center justify-between text-xs text-gray-500">
                        <span>Category: {ticket.category}</span>
                        <span>Created: {new Date(ticket.created_at).toLocaleDateString()}</span>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </Card>
          </div>
        )}
      </main>
    </div>
  );
};

export default function Support() {
  return (
    <AuthProvider>
      <SupportPage />
    </AuthProvider>
  );
}
