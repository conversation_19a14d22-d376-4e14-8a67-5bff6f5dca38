'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useAuth } from '@/contexts/AuthContext';
import AuthProvider from '@/contexts/AuthContext';
import Header from '@/components/layout/Header';
import Button from '@/components/ui/Button';
import Card from '@/components/ui/Card';
import { api } from '@/lib/api';
import {
  PlusIcon,
  FolderIcon,
  UsersIcon,
  ClipboardDocumentListIcon,
} from '@heroicons/react/24/outline';

interface Project {
  id: number;
  name: string;
  description: string;
  owner_id: number;
  is_public: boolean;
  is_archived: boolean;
  created_at: string;
  updated_at: string;
  board_count?: number;
  task_count?: number;
}

const ProjectsPage: React.FC = () => {
  const { isAuthenticated, user: _user } = useAuth();
  const [projects, setProjects] = useState<Project[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (isAuthenticated) {
      fetchProjects();
    }
  }, [isAuthenticated]);

  const fetchProjects = async () => {
    try {
      const response = await api.projects.list();
      setProjects(response.data.projects || []);
    } catch (error) {
      console.error('Failed to fetch projects:', error);
    } finally {
      setLoading(false);
    }
  };

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-primary-500 mb-4">
            Please sign in to view your projects
          </h1>
          <Link href="/">
            <Button variant="primary">Go to Home</Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-primary-500">Projects</h1>
            <p className="text-gray-600 mt-2">
              Manage your projects and collaborate with your team
            </p>
          </div>
          <Link href="/projects/new">
            <Button variant="primary">
              <PlusIcon className="h-5 w-5 mr-2" />
              New Project
            </Button>
          </Link>
        </div>

        {/* Loading State */}
        {loading && (
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto"></div>
            <p className="text-gray-600 mt-4">Loading projects...</p>
          </div>
        )}

        {/* Empty State */}
        {!loading && projects.length === 0 && (
          <div className="text-center py-12">
            <FolderIcon className="h-24 w-24 text-gray-300 mx-auto mb-4" />
            <h2 className="text-xl font-semibold text-gray-600 mb-2">
              No projects yet
            </h2>
            <p className="text-gray-500 mb-6">
              Create your first project to start organizing your work
            </p>
            <Link href="/projects/new">
              <Button variant="primary">
                <PlusIcon className="h-5 w-5 mr-2" />
                Create Project
              </Button>
            </Link>
          </div>
        )}

        {/* Projects Grid */}
        {!loading && projects.length > 0 && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {projects.map((project) => (
              <Card key={project.id} className="hover:border-primary-300 transition-colors h-full">
                <Link href={`/projects/${project.id}`} className="block">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex-1">
                      <h3 className="text-lg font-semibold text-primary-500 mb-2 hover:text-primary-600">
                        {project.name}
                      </h3>
                      <p className="text-gray-600 text-sm line-clamp-2">
                        {project.description}
                      </p>
                    </div>
                    {project.is_public && (
                      <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full ml-2">
                        Public
                      </span>
                    )}
                  </div>

                  <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                    <div className="flex items-center space-x-4">
                      <div className="flex items-center">
                        <ClipboardDocumentListIcon className="h-4 w-4 mr-1" />
                        <span>{project.board_count || 0} boards</span>
                      </div>
                      <div className="flex items-center">
                        <UsersIcon className="h-4 w-4 mr-1" />
                        <span>{project.task_count || 0} tasks</span>
                      </div>
                    </div>
                  </div>
                </Link>

                {/* Action Buttons */}
                <div className="flex space-x-2 mb-4">
                  <Link href={`/boards/new?project=${project.id}`} className="flex-1">
                    <Button variant="primary" size="sm" className="w-full">
                      <ClipboardDocumentListIcon className="h-4 w-4 mr-2" />
                      Add Board
                    </Button>
                  </Link>
                  <Link href={`/projects/${project.id}/boards`} className="flex-1">
                    <Button variant="secondary" size="sm" className="w-full">
                      View Boards
                    </Button>
                  </Link>
                </div>

                <div className="pt-4 border-t border-gray-200">
                  <p className="text-xs text-gray-400">
                    Updated {new Date(project.updated_at).toLocaleDateString()}
                  </p>
                </div>
              </Card>
            ))}
          </div>
        )}
      </main>
    </div>
  );
};

export default function Projects() {
  return (
    <AuthProvider>
      <ProjectsPage />
    </AuthProvider>
  );
}
