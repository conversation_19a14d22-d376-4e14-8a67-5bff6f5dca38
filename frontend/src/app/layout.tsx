import type { Metada<PERSON> } from "next";
import "./globals.css";
import AuthProvider from "@/contexts/AuthContext";

// Using system fonts to avoid network dependency during build
const fontClass = "font-sans";

export const metadata: Metadata = {
  title: "Plover - Project Management Tool",
  description: "A blockchain-based project management platform powered by Pi Network",
  keywords: ["project management", "blockchain", "pi network", "collaboration", "tasks"],
  authors: [{ name: "Plover Team" }],
  icons: {
    icon: "/icon.svg",
    apple: "/icon.svg",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <head>
        <script src="https://sdk.minepi.com/pi-sdk.js" async></script>
      </head>
      <body className={`${fontClass} antialiased`}>
        <AuthProvider>
          {children}
        </AuthProvider>
      </body>
    </html>
  );
}
