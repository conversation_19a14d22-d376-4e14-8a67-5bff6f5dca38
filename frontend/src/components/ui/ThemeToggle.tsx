'use client';

import React, { useState } from 'react';
import { useTheme } from '@/contexts/ThemeContext';
import {
  SunIcon,
  MoonIcon,
  ComputerDesktopIcon,
  ChevronDownIcon,
} from '@heroicons/react/24/outline';
import clsx from 'clsx';

const ThemeToggle: React.FC = () => {
  const { theme, actualTheme, setTheme } = useTheme();
  const [isOpen, setIsOpen] = useState(false);

  const themes = [
    {
      value: 'light' as const,
      label: 'Light',
      icon: SunIcon,
    },
    {
      value: 'dark' as const,
      label: 'Dark',
      icon: MoonIcon,
    },
    {
      value: 'system' as const,
      label: 'System',
      icon: ComputerDesktopIcon,
    },
  ];

  const currentTheme = themes.find(t => t.value === theme);
  const CurrentIcon = currentTheme?.icon || SunIcon;

  return (
    <div className="relative">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className={clsx(
          'flex items-center space-x-2 px-3 py-2 rounded-lg border transition-colors',
          'hover:bg-gray-50 dark:hover:bg-gray-800',
          'border-gray-200 dark:border-gray-700',
          'text-gray-700 dark:text-gray-300'
        )}
        aria-label="Toggle theme"
      >
        <CurrentIcon className="h-4 w-4" />
        <span className="text-sm font-medium">{currentTheme?.label}</span>
        <ChevronDownIcon 
          className={clsx(
            'h-4 w-4 transition-transform',
            isOpen && 'rotate-180'
          )} 
        />
      </button>

      {isOpen && (
        <>
          {/* Backdrop */}
          <div
            className="fixed inset-0 z-10"
            onClick={() => setIsOpen(false)}
          />
          
          {/* Dropdown */}
          <div className="absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-lg z-20">
            <div className="py-1">
              {themes.map((themeOption) => {
                const Icon = themeOption.icon;
                const isSelected = theme === themeOption.value;
                
                return (
                  <button
                    key={themeOption.value}
                    onClick={() => {
                      setTheme(themeOption.value);
                      setIsOpen(false);
                    }}
                    className={clsx(
                      'w-full flex items-center space-x-3 px-4 py-2 text-left transition-colors',
                      'hover:bg-gray-50 dark:hover:bg-gray-700',
                      isSelected 
                        ? 'text-primary-600 dark:text-primary-400 bg-primary-50 dark:bg-primary-900/20'
                        : 'text-gray-700 dark:text-gray-300'
                    )}
                  >
                    <Icon className="h-4 w-4" />
                    <span className="text-sm font-medium">
                      {themeOption.label}
                    </span>
                    {themeOption.value === 'system' && (
                      <span className="text-xs text-gray-500 dark:text-gray-400 ml-auto">
                        ({actualTheme})
                      </span>
                    )}
                    {isSelected && (
                      <div className="ml-auto">
                        <div className="w-2 h-2 bg-primary-600 dark:bg-primary-400 rounded-full" />
                      </div>
                    )}
                  </button>
                );
              })}
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default ThemeToggle;
