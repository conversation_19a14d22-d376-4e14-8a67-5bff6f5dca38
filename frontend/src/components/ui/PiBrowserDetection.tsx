'use client';

import React, { useState, useEffect } from 'react';
import Button from './Button';
import Card from './Card';
import {
  ExclamationTriangleIcon,
  InformationCircleIcon,
  XMarkIcon,
} from '@heroicons/react/24/outline';

interface PiBrowserDetectionProps {
  onDismiss?: () => void;
}

const PiBrowserDetection: React.FC<PiBrowserDetectionProps> = ({ onDismiss }) => {
  const [isPiBrowser, setIsPiBrowser] = useState<boolean | null>(null);
  const [dismissed, setDismissed] = useState(false);

  useEffect(() => {
    // Check if running in Pi Browser
    const checkPiBrowser = () => {
      // Check for Pi SDK availability
      if (typeof window !== 'undefined') {
        const hasPiSDK = !!(window as unknown as { Pi?: unknown }).Pi;
        const userAgent = navigator.userAgent.toLowerCase();
        const isPiUserAgent = userAgent.includes('pi browser') || userAgent.includes('pi/');
        
        setIsPiBrowser(hasPiSDK || isPiUserAgent);
      }
    };

    checkPiBrowser();
  }, []);

  const handleDismiss = () => {
    setDismissed(true);
    if (onDismiss) {
      onDismiss();
    }
  };

  // Don't show if Pi Browser is detected or if dismissed
  if (isPiBrowser === null || isPiBrowser || dismissed) {
    return null;
  }

  return (
    <div className="fixed top-4 left-4 right-4 z-50 max-w-md mx-auto">
      <Card className="border-yellow-200 bg-yellow-50">
        <div className="flex items-start">
          <div className="flex-shrink-0">
            <ExclamationTriangleIcon className="h-6 w-6 text-yellow-600" />
          </div>
          <div className="ml-3 flex-1">
            <h3 className="text-sm font-medium text-yellow-800 mb-2">
              Pi Browser Required
            </h3>
            <div className="text-sm text-yellow-700 space-y-2">
              <p>
                This app requires Pi Browser for authentication and payments.
              </p>
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mt-3">
                <div className="flex items-start">
                  <InformationCircleIcon className="h-5 w-5 text-blue-600 mt-0.5 mr-2 flex-shrink-0" />
                  <div className="text-sm text-blue-800">
                    <p className="font-medium mb-1">Support the Developer</p>
                    <p className="mb-2">
                      Pi is a new digital currency developed by Stanford PhDs, with over 55 million members worldwide.
                    </p>
                    <p className="mb-2">
                      To claim your Pi and support me as the developer:
                    </p>
                    <ol className="list-decimal list-inside space-y-1 text-xs">
                      <li>Download Pi Browser from the Pi Network app</li>
                      <li>Use this link: <strong>https://minepi.com/Pnguweneza</strong></li>
                      <li>Use invitation code: <strong>Pnguweneza</strong></li>
                    </ol>
                  </div>
                </div>
              </div>
              <div className="flex space-x-2 mt-4">
                <Button
                  variant="primary"
                  size="sm"
                  onClick={() => {
                    window.open('https://minepi.com/Pnguweneza', '_blank');
                  }}
                >
                  Get Pi & Support Dev
                </Button>
                <Button
                  variant="secondary"
                  size="sm"
                  onClick={handleDismiss}
                >
                  Continue Anyway
                </Button>
              </div>
            </div>
          </div>
          <div className="ml-4 flex-shrink-0">
            <button
              onClick={handleDismiss}
              className="rounded-md text-yellow-400 hover:text-yellow-500 focus:outline-none focus:ring-2 focus:ring-yellow-500"
            >
              <span className="sr-only">Dismiss</span>
              <XMarkIcon className="h-5 w-5" />
            </button>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default PiBrowserDetection;
