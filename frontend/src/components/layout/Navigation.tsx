'use client';

import React from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import {
  CheckCircleIcon,
  ClipboardDocumentListIcon,
  FolderIcon,
  UsersIcon,
} from '@heroicons/react/24/outline';
import clsx from 'clsx';

interface NavigationItem {
  name: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  requiresAuth?: boolean;
}

const navigationItems: NavigationItem[] = [
  {
    name: 'Tasks',
    href: '/tasks',
    icon: CheckCircleIcon,
    requiresAuth: true,
  },
  {
    name: 'Boards',
    href: '/boards',
    icon: ClipboardDocumentListIcon,
    requiresAuth: true,
  },
  {
    name: 'Projects',
    href: '/projects',
    icon: FolderIcon,
    requiresAuth: true,
  },
  {
    name: 'Team',
    href: '/team',
    icon: UsersIcon,
    requiresAuth: true,
  },
];

interface NavigationProps {
  className?: string;
  mobile?: boolean;
  onItemClick?: () => void;
}

const Navigation: React.FC<NavigationProps> = ({ 
  className = '', 
  mobile = false,
  onItemClick 
}) => {
  const pathname = usePathname();
  const { isAuthenticated } = useAuth();

  const filteredItems = navigationItems.filter(item => 
    !item.requiresAuth || isAuthenticated
  );

  const baseClasses = mobile 
    ? 'flex flex-col space-y-1' 
    : 'flex space-x-1';

  const linkClasses = (isActive: boolean) => clsx(
    'flex items-center px-3 py-2 rounded-lg text-sm font-medium transition-colors',
    mobile ? 'w-full' : '',
    isActive
      ? 'bg-primary-100 text-primary-700 dark:bg-primary-900/20 dark:text-primary-300'
      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100 dark:text-gray-300 dark:hover:text-gray-100 dark:hover:bg-gray-800'
  );

  return (
    <nav className={clsx(baseClasses, className)}>
      {filteredItems.map((item) => {
        const isActive = pathname === item.href || 
          (item.href !== '/' && pathname.startsWith(item.href));
        const Icon = item.icon;

        return (
          <Link
            key={item.name}
            href={item.href}
            className={linkClasses(isActive)}
            onClick={onItemClick}
          >
            <Icon className={clsx(
              'h-5 w-5',
              mobile ? 'mr-3' : 'mr-2'
            )} />
            {item.name}
          </Link>
        );
      })}
    </nav>
  );
};

export default Navigation;
