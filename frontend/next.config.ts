import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  // Handle Pi Network sandbox URL structure
  basePath: process.env.NODE_ENV === 'production' && process.env.NEXT_PUBLIC_PI_SANDBOX === 'true'
    ? '/mobile-app-ui/app/plover-aa6d59317de15685'
    : '',

  // Ensure assets work with base path
  assetPrefix: process.env.NODE_ENV === 'production' && process.env.NEXT_PUBLIC_PI_SANDBOX === 'true'
    ? '/mobile-app-ui/app/plover-aa6d59317de15685'
    : '',

  // Handle trailing slashes
  trailingSlash: false,

  // Rewrite rules for Pi sandbox
  async rewrites() {
    if (process.env.NODE_ENV === 'production' && process.env.NEXT_PUBLIC_PI_SANDBOX === 'true') {
      return [
        {
          source: '/mobile-app-ui/app/plover-aa6d59317de15685/:path*',
          destination: '/:path*',
        },
      ];
    }
    return [];
  },
};

export default nextConfig;
