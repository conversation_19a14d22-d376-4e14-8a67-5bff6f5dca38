/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  darkMode: 'class',
  theme: {
    extend: {
      colors: {
        primary: {
          50: '#f0f9f8',
          100: '#ccebe8',
          200: '#99d6d0',
          300: '#66c2b8',
          400: '#33ada0',
          500: '#074944',
          600: '#063a37',
          700: '#052c2a',
          800: '#041d1c',
          900: '#020f0e',
        },
        accent: {
          50: '#fefbf0',
          100: '#fdf4cc',
          200: '#fbe999',
          300: '#f9de66',
          400: '#f7d333',
          500: '#ECC41C',
          600: '#bd9d16',
          700: '#8e7611',
          800: '#5f4e0b',
          900: '#2f2706',
        },
      },
      fontFamily: {
        sans: ['var(--font-inter)', 'system-ui', 'sans-serif'],
      },
    },
  },
  plugins: [],
};
