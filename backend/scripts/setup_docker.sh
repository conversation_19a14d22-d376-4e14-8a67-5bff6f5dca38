#!/bin/bash

# Plover PM Tool - Docker Setup Script
# Installs Docker + Docker Compose plugin on Ubuntu/Debian

set -e

echo "🐳 Setting up Docker for Plover PM Tool..."

# Prevent running as root
if [[ $EUID -eq 0 ]]; then
   echo "❌ Do not run this script as root. Use a normal user."
   exit 1
fi

# Detect OS (Ubuntu or Debian)
DISTRO=$(lsb_release -is | tr '[:upper:]' '[:lower:]')
CODENAME=$(lsb_release -cs)

echo "🖥️ Detected OS: $DISTRO ($CODENAME)"

# Update package index
echo "📦 Updating package index..."
sudo apt-get update

# Install prerequisites
echo "🔧 Installing prerequisites..."
sudo apt-get install -y \
    ca-certificates \
    curl \
    gnupg \
    lsb-release

# Add Docker GPG key
echo "🔑 Adding Docker GPG key..."
sudo mkdir -p /etc/apt/keyrings
curl -fsSL https://download.docker.com/linux/$DISTRO/gpg | sudo gpg --dearmor -o /etc/apt/keyrings/docker.gpg

# Add Docker repository
echo "📋 Setting up Docker repository..."
echo \
  "deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/docker.gpg] https://download.docker.com/linux/$DISTRO \
  $CODENAME stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null

# Update package index again
sudo apt-get update

# Install Docker Engine + plugins
echo "🐳 Installing Docker Engine..."
sudo apt-get install -y docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin

# Add current user to docker group
echo "👤 Adding user to docker group..."
sudo usermod -aG docker $USER

# Start and enable Docker service
echo "🚀 Starting Docker service..."
sudo systemctl enable docker
sudo systemctl start docker

# Test Docker
echo "🧪 Testing Docker installation..."
if docker run --rm hello-world > /dev/null 2>&1; then
    echo "✅ Docker installed successfully!"
else
    echo "❌ Docker installation failed!"
    exit 1
fi

echo ""
echo "🎉 Docker setup complete!"
echo ""
echo "📝 Next steps:"
echo "1. Log out and log back in (or run 'newgrp docker') to use Docker without sudo"
echo "2. Navigate to the backend directory: cd backend"
echo "3. Start services: docker compose up -d"
echo "4. Check logs: docker compose logs -f"
echo "5. Stop services: docker compose down"
echo ""
echo "🌐 API will be available at:"
echo "   - API: http://localhost:8000"
echo "   - API Docs: http://localhost:8000/docs"
echo "   - Database: localhost:5432"
echo ""
