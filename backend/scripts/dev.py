#!/usr/bin/env python3
"""
Development script for the Plover Project Management Tool backend
"""

import asyncio
import sys
import os
from pathlib import Path
from datetime import datetime, timedelta

# Add the backend directory to the Python path
backend_dir = Path(__file__).parent.parent
sys.path.insert(0, str(backend_dir))

from app.database.connection import init_db, close_db
from app.models.user import User
from app.models.task import Task, TaskStatus, TaskPriority
from app.models.board import Board, BoardMember
from app.models.payment import Payment, Subscription, PaymentStatus, SubscriptionStatus, SubscriptionPlan
from app.database.utils import DatabaseUtils
from app.database.connection import get_db_session


async def create_sample_data():
    """Create sample data for development"""
    print("Creating sample data...")
    
    async with get_db_session() as db:
        try:
            # Create sample users
            admin_user = await DatabaseUtils.create(
                db, User,
                uid="admin_uid_123",
                username="admin",
                roles=["admin", "user"],
                access_token="sample_admin_token",
                is_active=True,
                is_verified=True
            )
            
            regular_user = await DatabaseUtils.create(
                db, User,
                uid="user_uid_456",
                username="johndoe",
                roles=["user"],
                access_token="sample_user_token",
                is_active=True,
                is_verified=True
            )
            
            # Create sample board
            board = await DatabaseUtils.create(
                db, Board,
                name="Sample Project Board",
                description="A sample board for development",
                owner_id=admin_user.id,
                is_public=False
            )
            
            # Add board members
            await DatabaseUtils.create(
                db, BoardMember,
                board_id=board.id,
                user_id=admin_user.id,
                role="owner",
                can_edit=True,
                can_delete=True,
                can_invite=True
            )
            
            await DatabaseUtils.create(
                db, BoardMember,
                board_id=board.id,
                user_id=regular_user.id,
                role="member",
                can_edit=True,
                can_delete=False,
                can_invite=False
            )
            
            # Create sample tasks
            tasks_data = [
                {
                    "title": "Set up project structure",
                    "description": "Initialize the project with proper folder structure",
                    "status": TaskStatus.COMPLETED,
                    "priority": TaskPriority.HIGH
                },
                {
                    "title": "Implement user authentication",
                    "description": "Add Pi Network authentication integration",
                    "status": TaskStatus.IN_PROGRESS,
                    "priority": TaskPriority.HIGH
                },
                {
                    "title": "Create task management system",
                    "description": "Build CRUD operations for tasks",
                    "status": TaskStatus.IN_PROGRESS,
                    "priority": TaskPriority.MEDIUM
                },
                {
                    "title": "Design user interface",
                    "description": "Create mockups and wireframes for the UI",
                    "status": TaskStatus.TODO,
                    "priority": TaskPriority.MEDIUM
                },
                {
                    "title": "Write documentation",
                    "description": "Document API endpoints and usage",
                    "status": TaskStatus.TODO,
                    "priority": TaskPriority.LOW
                }
            ]
            
            for i, task_data in enumerate(tasks_data):
                assignee_id = admin_user.id if i % 2 == 0 else regular_user.id
                await DatabaseUtils.create(
                    db, Task,
                    title=task_data["title"],
                    description=task_data["description"],
                    creator_id=admin_user.id,
                    assignee_id=assignee_id,
                    status=task_data["status"],
                    priority=task_data["priority"],
                    board_id=board.id
                )
            
            # Create sample payment and subscription
            payment = await DatabaseUtils.create(
                db, Payment,
                pi_payment_id="sample_payment_123",
                user_id=regular_user.id,
                amount=15.00,
                memo="Standard Plan Subscription",
                status=PaymentStatus.COMPLETED,
                txid="sample_tx_456"
            )
            
            await DatabaseUtils.create(
                db, Subscription,
                user_id=regular_user.id,
                plan=SubscriptionPlan.STANDARD,
                status=SubscriptionStatus.ACTIVE,
                start_date=datetime.utcnow(),
                end_date=datetime.utcnow() + timedelta(days=30),  # 30 days
                payment_id=payment.id,
                features='["Advanced task management", "Unlimited boards", "50 team members"]'
            )
            
            print("✅ Sample data created successfully!")
            print(f"Admin user: {admin_user.username} (uid: {admin_user.uid})")
            print(f"Regular user: {regular_user.username} (uid: {regular_user.uid})")
            print(f"Board: {board.name}")
            print(f"Tasks created: {len(tasks_data)}")
            
        except Exception as e:
            print(f"❌ Error creating sample data: {e}")
            raise


async def reset_database():
    """Reset the database (drop and recreate tables)"""
    print("Resetting database...")
    
    try:
        from sqlmodel import SQLModel
        from app.database.connection import engine
        
        # Drop all tables
        async with engine.begin() as conn:
            await conn.run_sync(SQLModel.metadata.drop_all)
        
        # Recreate all tables
        await init_db()
        
        print("✅ Database reset successfully!")
        
    except Exception as e:
        print(f"❌ Error resetting database: {e}")
        raise


async def main():
    """Main development script"""
    if len(sys.argv) < 2:
        print("Usage: python scripts/dev.py <command>")
        print("Commands:")
        print("  init-db     - Initialize database tables")
        print("  reset-db    - Reset database (drop and recreate tables)")
        print("  sample-data - Create sample data for development")
        print("  setup       - Reset database and create sample data")
        return
    
    command = sys.argv[1]
    
    try:
        if command == "init-db":
            await init_db()
            print("✅ Database initialized!")
            
        elif command == "reset-db":
            await reset_database()
            
        elif command == "sample-data":
            await create_sample_data()
            
        elif command == "setup":
            await reset_database()
            await create_sample_data()
            
        else:
            print(f"Unknown command: {command}")
            
    except Exception as e:
        print(f"❌ Command failed: {e}")
        sys.exit(1)
    
    finally:
        await close_db()


if __name__ == "__main__":
    asyncio.run(main())
