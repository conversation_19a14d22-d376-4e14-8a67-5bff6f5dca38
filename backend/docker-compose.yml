services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: plover-postgres
    environment:
      POSTGRES_DB: plover_pm
      POSTGRES_USER: plover_user
      POSTGRES_PASSWORD: plover_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    networks:
      - plover-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U plover_user -d plover_pm"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis (for caching and sessions)
  redis:
    image: redis:7-alpine
    container_name: plover-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - plover-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # FastAPI Backend
  backend:
    build: .
    container_name: plover-backend
    environment:
      - ENVIRONMENT=production
      - DATABASE_URL=postgresql+asyncpg://plover_user:plover_password@postgres:5432/plover_pm
      - PI_API_KEY=${PI_API_KEY}
      - SECRET_KEY=${SECRET_KEY}
      - ALLOWED_ORIGINS=${ALLOWED_ORIGINS:-http://localhost:3000}
      - ALLOWED_HOSTS=${ALLOWED_HOSTS:-localhost,127.0.0.1}
    ports:
      - "8000:8000"
    volumes:
      - ./logs:/app/logs
    networks:
      - plover-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: plover-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    networks:
      - plover-network
    depends_on:
      - backend
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:

networks:
  plover-network:
    driver: bridge
