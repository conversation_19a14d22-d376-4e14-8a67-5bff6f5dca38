"""
User management API endpoints
"""

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List

from app.database.connection import get_db
from app.schemas.user import UserResponse, UserProfile, UserStats
from app.core.dependencies import get_current_active_user, require_admin
from app.models.user import User
from app.models.task import Task, TaskStatus
from app.models.board import Board
from app.models.project import Project
from app.database.utils import DatabaseUtils
from sqlalchemy import func, select

router = APIRouter()


@router.get("/profile", response_model=UserProfile)
async def get_user_profile(
    current_user: User = Depends(get_current_active_user)
):
    """Get current user profile"""
    return UserProfile.model_validate(current_user)


@router.get("/", response_model=List[UserProfile])
async def list_users(
    skip: int = 0,
    limit: int = 100,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(require_admin())
):
    """List all users (admin only)"""
    users = await DatabaseUtils.get_all(db, User, skip=skip, limit=limit)
    return [UserProfile.model_validate(user) for user in users]


@router.get("/stats", response_model=UserStats)
async def get_user_stats(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Get current user's statistics"""
    # Get task statistics
    tasks_completed_query = select(func.count(Task.id)).where(
        Task.assignee_id == current_user.id,
        Task.status == TaskStatus.COMPLETED
    )
    tasks_completed_result = await db.execute(tasks_completed_query)
    tasks_completed = tasks_completed_result.scalar() or 0

    tasks_in_progress_query = select(func.count(Task.id)).where(
        Task.assignee_id == current_user.id,
        Task.status == TaskStatus.IN_PROGRESS
    )
    tasks_in_progress_result = await db.execute(tasks_in_progress_query)
    tasks_in_progress = tasks_in_progress_result.scalar() or 0

    # Get boards created by user
    boards_created_query = select(func.count(Board.id)).where(
        Board.owner_id == current_user.id
    )
    boards_created_result = await db.execute(boards_created_query)
    boards_created = boards_created_result.scalar() or 0

    # Get projects owned by user
    projects_owned_query = select(func.count(Project.id)).where(
        Project.owner_id == current_user.id
    )
    projects_owned_result = await db.execute(projects_owned_query)
    projects_owned = projects_owned_result.scalar() or 0

    # For now, set team_members to 0 as we don't have a team members relationship yet
    team_members = 0

    return UserStats(
        tasks_completed=tasks_completed,
        tasks_in_progress=tasks_in_progress,
        boards_created=boards_created,
        projects_owned=projects_owned,
        team_members=team_members
    )


@router.get("/{user_id}", response_model=UserProfile)
async def get_user(
    user_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Get user by ID"""
    user = await DatabaseUtils.get_by_id(db, User, user_id)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    return UserProfile.model_validate(user)
