"""
Support ticket API endpoints
"""

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_
from typing import List, Optional
from datetime import datetime

from app.database.connection import get_db
from app.core.dependencies import get_current_active_user
from app.models.user import User
from app.models.support_ticket import SupportTicket
from app.schemas.support import SupportTicketCreate, SupportTicketResponse, SupportTicketUpdate

router = APIRouter()


@router.post("/tickets/", response_model=SupportTicketResponse)
async def create_support_ticket(
    ticket_data: SupportTicketCreate,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Create a new support ticket
    
    Args:
        ticket_data: Support ticket data
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        Created support ticket
    """
    try:
        # Create support ticket
        support_ticket = SupportTicket(
            user_id=current_user.id,
            ticket_number=ticket_data.ticket_number,
            subject=ticket_data.subject,
            message=ticket_data.message,
            category=ticket_data.category,
            status=ticket_data.status or "OPEN",
            priority=ticket_data.priority or "MEDIUM"
        )
        
        db.add(support_ticket)
        await db.commit()
        await db.refresh(support_ticket)
        
        return SupportTicketResponse.model_validate(support_ticket)
        
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create support ticket"
        )


@router.get("/tickets/", response_model=List[SupportTicketResponse])
async def get_user_support_tickets(
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db),
    status_filter: Optional[str] = None,
    category_filter: Optional[str] = None
):
    """
    Get current user's support tickets
    
    Args:
        current_user: Current authenticated user
        db: Database session
        status_filter: Filter by status
        category_filter: Filter by category
        
    Returns:
        List of user's support tickets
    """
    try:
        query = select(SupportTicket).where(SupportTicket.user_id == current_user.id)
        
        if status_filter:
            query = query.where(SupportTicket.status == status_filter)
        if category_filter:
            query = query.where(SupportTicket.category == category_filter)
            
        query = query.order_by(SupportTicket.created_at.desc())
        
        result = await db.execute(query)
        tickets = result.scalars().all()
        
        return [SupportTicketResponse.model_validate(ticket) for ticket in tickets]
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to fetch support tickets"
        )


@router.get("/tickets/{ticket_id}", response_model=SupportTicketResponse)
async def get_support_ticket(
    ticket_id: str,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get a specific support ticket
    
    Args:
        ticket_id: Support ticket ID
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        Support ticket details
    """
    try:
        query = select(SupportTicket).where(
            and_(
                SupportTicket.id == ticket_id,
                SupportTicket.user_id == current_user.id
            )
        )
        
        result = await db.execute(query)
        ticket = result.scalar_one_or_none()
        
        if not ticket:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Support ticket not found"
            )
        
        return SupportTicketResponse.model_validate(ticket)
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to fetch support ticket"
        )


@router.put("/tickets/{ticket_id}", response_model=SupportTicketResponse)
async def update_support_ticket(
    ticket_id: str,
    ticket_update: SupportTicketUpdate,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Update a support ticket (user can only update their own tickets)
    
    Args:
        ticket_id: Support ticket ID
        ticket_update: Updated ticket data
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        Updated support ticket
    """
    try:
        query = select(SupportTicket).where(
            and_(
                SupportTicket.id == ticket_id,
                SupportTicket.user_id == current_user.id
            )
        )
        
        result = await db.execute(query)
        ticket = result.scalar_one_or_none()
        
        if not ticket:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Support ticket not found"
            )
        
        # Update ticket fields
        update_data = ticket_update.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(ticket, field, value)
        
        ticket.updated_at = datetime.utcnow()
        
        await db.commit()
        await db.refresh(ticket)
        
        return SupportTicketResponse.model_validate(ticket)
        
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update support ticket"
        )


@router.delete("/tickets/{ticket_id}")
async def delete_support_ticket(
    ticket_id: str,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Delete a support ticket (user can only delete their own tickets)
    
    Args:
        ticket_id: Support ticket ID
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        Success message
    """
    try:
        query = select(SupportTicket).where(
            and_(
                SupportTicket.id == ticket_id,
                SupportTicket.user_id == current_user.id
            )
        )
        
        result = await db.execute(query)
        ticket = result.scalar_one_or_none()
        
        if not ticket:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Support ticket not found"
            )
        
        await db.delete(ticket)
        await db.commit()
        
        return {"message": "Support ticket deleted successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete support ticket"
        )
