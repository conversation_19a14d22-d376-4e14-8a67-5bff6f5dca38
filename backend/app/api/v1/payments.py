"""
Payment processing API endpoints
"""

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List

from app.database.connection import get_db
from app.schemas.payment import (
    PaymentApprovalRequest, PaymentCompletionRequest, PaymentCancellationRequest,
    IncompletePaymentRequest, PaymentResponse, SubscriptionResponse,
    SubscriptionPlansResponse
)
from app.services.payment_service import payment_service
from app.core.dependencies import get_current_active_user
from app.models.user import User

router = APIRouter()


@router.get("/plans", response_model=SubscriptionPlansResponse)
async def get_subscription_plans():
    """Get available subscription plans"""
    return await payment_service.get_subscription_plans()


@router.post("/approve")
async def approve_payment(
    approval_request: PaymentApprovalRequest,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Approve payment with Pi Platform"""
    try:
        success = await payment_service.approve_payment(
            db, approval_request.paymentId, current_user
        )
        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Payment approval failed"
            )
        return {"message": f"Approved payment {approval_request.paymentId}"}
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Payment approval failed"
        )


@router.post("/complete")
async def complete_payment(
    completion_request: PaymentCompletionRequest,
    db: AsyncSession = Depends(get_db)
):
    """Complete payment with Pi Platform"""
    try:
        success = await payment_service.complete_payment(
            db, completion_request.paymentId, completion_request.txid
        )
        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Payment completion failed"
            )
        return {"message": f"Completed payment {completion_request.paymentId}"}
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Payment completion failed"
        )


@router.post("/cancel")
async def cancel_payment(
    cancellation_request: PaymentCancellationRequest,
    db: AsyncSession = Depends(get_db)
):
    """Cancel payment"""
    try:
        success = await payment_service.cancel_payment(
            db, cancellation_request.paymentId
        )
        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Payment cancellation failed"
            )
        return {"message": f"Cancelled payment {cancellation_request.paymentId}"}
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Payment cancellation failed"
        )


@router.post("/incomplete")
async def handle_incomplete_payment(
    incomplete_request: IncompletePaymentRequest,
    db: AsyncSession = Depends(get_db)
):
    """Handle incomplete payment from Pi Platform"""
    try:
        success = await payment_service.handle_incomplete_payment(
            db, incomplete_request.payment
        )
        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Incomplete payment handling failed"
            )
        return {"message": "Handled incomplete payment"}
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Incomplete payment handling failed"
        )


@router.get("/subscriptions", response_model=List[SubscriptionResponse])
async def get_user_subscriptions(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Get current user's subscriptions"""
    subscriptions = await payment_service.get_user_subscriptions(db, current_user)
    return subscriptions


@router.get("/subscription/active", response_model=SubscriptionResponse)
async def get_active_subscription(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Get current user's active subscription"""
    subscription = await payment_service.get_active_subscription(db, current_user)
    if not subscription:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="No active subscription found"
        )
    return subscription
