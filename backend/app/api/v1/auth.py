"""
Authentication API endpoints
"""

from fastapi import API<PERSON><PERSON><PERSON>, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from datetime import timedelta

from app.database.connection import get_db
from app.schemas.user import PiSignInRequest, UserResponse
from app.services.auth_service import auth_service
from app.core.dependencies import get_current_active_user
from app.models.user import User
from app.core.config import settings

router = APIRouter()


@router.post("/signin", response_model=dict)
async def pi_signin(
    signin_request: PiSignInRequest,
    db: AsyncSession = Depends(get_db)
):
    """
    Sign in with Pi Network authentication
    
    Args:
        signin_request: Pi Network authentication result
        db: Database session
        
    Returns:
        JWT access token and user information
    """
    try:
        auth_result = signin_request.authResult
        
        # Authenticate user with Pi Network
        user = await auth_service.authenticate_pi_user(
            db=db,
            access_token=auth_result.accessToken,
            user_data=auth_result.user
        )
        
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Pi Network authentication failed"
            )
        
        # Create JWT access token
        access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token = auth_service.create_access_token(
            data={"sub": user.uid, "username": user.username},
            expires_delta=access_token_expires
        )
        
        return {
            "access_token": access_token,
            "token_type": "bearer",
            "expires_in": settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,
            "user": user
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Authentication failed"
        )


@router.post("/refresh", response_model=dict)
async def refresh_token(
    refresh_request: dict,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Refresh Pi Network access token
    
    Args:
        refresh_request: Contains new Pi access token
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        New JWT access token
    """
    try:
        new_pi_token = refresh_request.get("pi_access_token")
        if not new_pi_token:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Pi access token required"
            )
        
        # Refresh Pi token
        success = await auth_service.refresh_pi_token(db, current_user, new_pi_token)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Token refresh failed"
            )
        
        # Create new JWT access token
        access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token = auth_service.create_access_token(
            data={"sub": current_user.uid, "username": current_user.username},
            expires_delta=access_token_expires
        )
        
        return {
            "access_token": access_token,
            "token_type": "bearer",
            "expires_in": settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Token refresh failed"
        )


@router.post("/signout")
async def signout(
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Sign out current user
    
    Args:
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        Success message
    """
    try:
        # Clear Pi access token
        await auth_service.refresh_pi_token(db, current_user, None)
        
        return {"message": "Successfully signed out"}
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Sign out failed"
        )


@router.get("/me", response_model=UserResponse)
async def get_current_user_info(
    current_user: User = Depends(get_current_active_user)
):
    """
    Get current user information

    Args:
        current_user: Current authenticated user

    Returns:
        Current user information
    """
    return UserResponse.model_validate(current_user)


@router.post("/deactivate")
async def deactivate_account(
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Deactivate current user account
    
    Args:
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        Success message
    """
    try:
        success = await auth_service.deactivate_user(db, current_user)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Account deactivation failed"
            )
        
        return {"message": "Account deactivated successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Account deactivation failed"
        )
