"""
Database connection and session management using SQLModel
"""

from sqlmodel import SQLModel
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
from sqlalchemy.pool import StaticPool
from app.core.config import settings, get_database_url
import logging

logger = logging.getLogger(__name__)

# Create async engine
engine = create_async_engine(
    get_database_url(),
    echo=settings.DATABASE_ECHO,
    # SQLite specific settings
    poolclass=StaticPool if "sqlite" in get_database_url() else None,
    connect_args={"check_same_thread": False} if "sqlite" in get_database_url() else {},
)

# Create session factory
AsyncSessionLocal = async_sessionmaker(
    engine,
    class_=AsyncSession,
    expire_on_commit=False
)


async def init_db():
    """Initialize database tables"""
    try:
        async with engine.begin() as conn:
            # Create all tables using SQLModel
            await conn.run_sync(SQLModel.metadata.create_all)
        logger.info("Database initialized successfully")
    except Exception as e:
        logger.error(f"Error initializing database: {e}")
        raise


async def close_db():
    """Close database connections"""
    try:
        await engine.dispose()
        logger.info("Database connections closed")
    except Exception as e:
        logger.error(f"Error closing database: {e}")


async def get_db() -> AsyncSession:
    """Get database session"""
    async with AsyncSessionLocal() as session:
        try:
            yield session
        except Exception as e:
            await session.rollback()
            logger.error(f"Database session error: {e}")
            raise
        finally:
            await session.close()


def get_db_session():
    """Get database session for direct use as context manager"""
    return AsyncSessionLocal()
