"""
Database utility functions
"""

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func
from typing import Type, TypeVar, Optional, List, Dict, Any
from app.models.base import BaseModel

T = TypeVar('T', bound=BaseModel)


class DatabaseUtils:
    """Database utility class with common operations"""
    
    @staticmethod
    async def get_by_id(
        db: AsyncSession, 
        model: Type[T], 
        id: int
    ) -> Optional[T]:
        """Get a record by ID"""
        result = await db.execute(select(model).where(model.id == id))
        return result.scalar_one_or_none()
    
    @staticmethod
    async def get_by_field(
        db: AsyncSession,
        model: Type[T],
        field_name: str,
        field_value: Any
    ) -> Optional[T]:
        """Get a record by a specific field"""
        field = getattr(model, field_name)
        result = await db.execute(select(model).where(field == field_value))
        return result.scalar_one_or_none()
    
    @staticmethod
    async def get_all(
        db: AsyncSession,
        model: Type[T],
        skip: int = 0,
        limit: int = 100,
        filters: Optional[Dict[str, Any]] = None
    ) -> List[T]:
        """Get all records with optional filtering and pagination"""
        query = select(model)
        
        # Apply filters
        if filters:
            for field_name, field_value in filters.items():
                if hasattr(model, field_name):
                    field = getattr(model, field_name)
                    query = query.where(field == field_value)
        
        # Apply pagination
        query = query.offset(skip).limit(limit)
        
        result = await db.execute(query)
        return result.scalars().all()
    
    @staticmethod
    async def count(
        db: AsyncSession,
        model: Type[T],
        filters: Optional[Dict[str, Any]] = None
    ) -> int:
        """Count records with optional filtering"""
        query = select(func.count(model.id))
        
        # Apply filters
        if filters:
            for field_name, field_value in filters.items():
                if hasattr(model, field_name):
                    field = getattr(model, field_name)
                    query = query.where(field == field_value)
        
        result = await db.execute(query)
        return result.scalar()
    
    @staticmethod
    async def create(
        db: AsyncSession,
        model: Type[T],
        **kwargs
    ) -> T:
        """Create a new record"""
        instance = model(**kwargs)
        db.add(instance)
        await db.commit()
        await db.refresh(instance)
        return instance
    
    @staticmethod
    async def update(
        db: AsyncSession,
        instance: T,
        **kwargs
    ) -> T:
        """Update an existing record"""
        for field, value in kwargs.items():
            if hasattr(instance, field):
                setattr(instance, field, value)
        
        await db.commit()
        await db.refresh(instance)
        return instance
    
    @staticmethod
    async def delete(
        db: AsyncSession,
        instance: T
    ) -> bool:
        """Delete a record"""
        try:
            await db.delete(instance)
            await db.commit()
            return True
        except Exception:
            await db.rollback()
            return False
    
    @staticmethod
    async def exists(
        db: AsyncSession,
        model: Type[T],
        **kwargs
    ) -> bool:
        """Check if a record exists"""
        query = select(model)
        for field_name, field_value in kwargs.items():
            if hasattr(model, field_name):
                field = getattr(model, field_name)
                query = query.where(field == field_value)
        
        result = await db.execute(query.limit(1))
        return result.scalar_one_or_none() is not None
