"""
Application configuration settings
"""

from pydantic_settings import BaseSettings
from typing import List, Optional, Union
import os


class Settings(BaseSettings):
    """Application settings"""
    
    # Application
    APP_NAME: str = "Plover Project Management Tool"
    APP_VERSION: str = "1.0.0"
    ENVIRONMENT: str = "development"
    DEBUG: bool = True
    HOST: str = "0.0.0.0"
    PORT: int = 8000
    
    # Security
    SECRET_KEY: str = "your-secret-key-change-in-production"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    ALGORITHM: str = "HS256"
    
    # CORS
    ALLOWED_ORIGINS: Union[str, List[str]] = ["http://localhost:3000", "http://127.0.0.1:3000"]
    ALLOWED_HOSTS: Union[str, List[str]] = ["localhost", "127.0.0.1"]
    
    # Database
    DATABASE_URL: str = "sqlite+aiosqlite:///./plover_pm.db"
    DATABASE_ECHO: bool = False
    
    # Pi Network Integration
    PI_API_KEY: str = ""
    PI_PLATFORM_API_URL: str = "https://api.minepi.com"
    PI_SANDBOX_MODE: bool = True
    
    # Frontend URLs
    FRONTEND_URL: str = "http://localhost:3000"
    
    # Logging
    LOG_LEVEL: str = "INFO"

    # Rate Limiting
    RATE_LIMIT_CALLS: int = 100
    RATE_LIMIT_PERIOD: int = 60

    class Config:
        env_file = ".env"
        case_sensitive = True
        extra = "ignore"  # Allow extra fields from .env


# Create settings instance
settings = Settings()


def get_database_url() -> str:
    """Get database URL based on environment"""
    if settings.ENVIRONMENT == "production":
        # Use PostgreSQL in production
        return settings.DATABASE_URL.replace("sqlite+aiosqlite://", "postgresql+asyncpg://")
    else:
        # Use SQLite in development
        return settings.DATABASE_URL


def get_cors_origins() -> List[str]:
    """Get CORS origins"""
    if isinstance(settings.ALLOWED_ORIGINS, str):
        return [origin.strip() for origin in settings.ALLOWED_ORIGINS.split(",")]
    return settings.ALLOWED_ORIGINS


def get_allowed_hosts() -> List[str]:
    """Get allowed hosts"""
    if isinstance(settings.ALLOWED_HOSTS, str):
        return [host.strip() for host in settings.ALLOWED_HOSTS.split(",")]
    return settings.ALLOWED_HOSTS
