"""
Authorization decorators and middleware for FastAPI endpoints
"""

from functools import wraps
from typing import Callable, Dict, Any, Optional
from fastapi import HTTPException, Depends, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.database.connection import get_db
from app.core.dependencies import get_current_active_user
from app.services.authorization import AuthorizationService, Permission
from app.models.user import User
from app.models.project import Project, ProjectMember
from app.models.board import Board, BoardMember
from app.models.task import Task


def require_permission(permission: Permission, resource_type: str):
    """
    Decorator to require specific permission for an endpoint
    
    Args:
        permission: The permission required
        resource_type: Type of resource (project, board, task)
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # Extract common parameters
            current_user = kwargs.get('current_user')
            db = kwargs.get('db')
            
            if not current_user or not db:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Authentication required"
                )
            
            # Get resource ID from path parameters
            resource_id = None
            if resource_type == "project":
                resource_id = kwargs.get('project_id') or kwargs.get('id')
            elif resource_type == "board":
                resource_id = kwargs.get('board_id') or kwargs.get('id')
            elif resource_type == "task":
                resource_id = kwargs.get('task_id') or kwargs.get('id')
            
            if resource_id:
                # Load resource and check permissions
                resource_data = await _load_resource_data(db, resource_type, resource_id)
                context = await _build_authorization_context(db, current_user.id, resource_data)
                
                if not AuthorizationService.has_permission(
                    current_user.id, permission, resource_type, resource_data, context
                ):
                    raise HTTPException(
                        status_code=status.HTTP_403_FORBIDDEN,
                        detail=f"Insufficient permissions for {permission.value}"
                    )
            
            return await func(*args, **kwargs)
        return wrapper
    return decorator


async def _load_resource_data(db: AsyncSession, resource_type: str, resource_id: int) -> Dict[str, Any]:
    """Load resource data for authorization"""
    
    if resource_type == "project":
        from app.database.utils import DatabaseUtils
        project = await DatabaseUtils.get_by_id(db, Project, resource_id)
        if not project:
            raise HTTPException(status_code=404, detail="Project not found")

        return {
            "id": project.id,
            "created_by": project.created_by,
            "is_public": project.is_public,
            "name": project.name
        }
    
    elif resource_type == "board":
        from app.database.utils import DatabaseUtils
        board = await DatabaseUtils.get_by_id(db, Board, resource_id)
        if not board:
            raise HTTPException(status_code=404, detail="Board not found")

        board_data = {
            "id": board.id,
            "created_by": board.created_by,
            "is_public": board.is_public,
            "name": board.name,
            "project_id": board.project_id
        }

        # Include project data if board belongs to a project
        if board.project_id:
            project = await DatabaseUtils.get_by_id(db, Project, board.project_id)
            if project:
                board_data["project"] = {
                    "id": project.id,
                    "created_by": project.created_by,
                    "is_public": project.is_public
                }

        return board_data
    
    elif resource_type == "task":
        from app.database.utils import DatabaseUtils
        task = await DatabaseUtils.get_by_id(db, Task, resource_id)
        if not task:
            raise HTTPException(status_code=404, detail="Task not found")

        task_data = {
            "id": task.id,
            "created_by": task.created_by,
            "assignee_id": task.assignee_id,
            "board_id": task.board_id,
            "title": task.title
        }

        # Include board data
        if task.board_id:
            board = await DatabaseUtils.get_by_id(db, Board, task.board_id)
            if board:
                task_data["board"] = {
                    "id": board.id,
                    "created_by": board.created_by,
                    "project_id": board.project_id
                }

                # Include project data if board belongs to a project
                if board.project_id:
                    project = await DatabaseUtils.get_by_id(db, Project, board.project_id)
                    if project:
                        task_data["board"]["project"] = {
                            "id": project.id,
                            "created_by": project.created_by
                        }

        return task_data
    
    raise HTTPException(status_code=400, detail=f"Unknown resource type: {resource_type}")


async def _build_authorization_context(
    db: AsyncSession,
    user_id: int,
    resource_data: Dict[str, Any]
) -> Dict[str, Any]:
    """Build authorization context with user memberships"""
    
    context = {
        "project_memberships": []
    }
    
    # Get all project memberships for the user
    from app.database.utils import DatabaseUtils
    from sqlalchemy import select

    stmt = select(ProjectMember).where(ProjectMember.user_id == user_id)
    result = await db.execute(stmt)
    project_memberships = result.scalars().all()

    for membership in project_memberships:
        context["project_memberships"].append({
            "user_id": membership.user_id,
            "project_id": membership.project_id,
            "is_manager": membership.is_manager
        })
    
    return context


async def get_current_user_permissions(
    resource_type: str,
    resource_id: int,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
) -> list[Permission]:
    """
    Dependency to get current user's permissions for a resource
    """
    resource_data = await _load_resource_data(db, resource_type, resource_id)
    context = await _build_authorization_context(db, current_user.id, resource_data)

    return AuthorizationService.get_user_permissions(
        current_user.id, resource_type, resource_data, context
    )


class AuthorizationChecker:
    """Helper class for authorization checks in endpoints"""
    
    def __init__(self, db: Session, current_user: User):
        self.db = db
        self.current_user = current_user
    
    async def can_edit_project(self, project_id: int) -> bool:
        """Check if user can edit project"""
        resource_data = await _load_resource_data(self.db, "project", project_id)
        context = await _build_authorization_context(self.db, self.current_user.id, resource_data)
        
        return AuthorizationService.has_permission(
            self.current_user.id, Permission.EDIT_PROJECT, "project", resource_data, context
        )
    
    async def can_delete_board(self, board_id: int) -> bool:
        """Check if user can delete board"""
        resource_data = await _load_resource_data(self.db, "board", board_id)
        context = await _build_authorization_context(self.db, self.current_user.id, resource_data)
        
        return AuthorizationService.has_permission(
            self.current_user.id, Permission.DELETE_BOARD, "board", resource_data, context
        )
    
    async def can_update_task(self, task_id: int) -> bool:
        """Check if user can update task"""
        resource_data = await _load_resource_data(self.db, "task", task_id)
        context = await _build_authorization_context(self.db, self.current_user.id, resource_data)
        
        return AuthorizationService.has_permission(
            self.current_user.id, Permission.EDIT_TASK, "task", resource_data, context
        )
    
    async def can_assign_task(self, task_id: int) -> bool:
        """Check if user can assign task"""
        resource_data = await _load_resource_data(self.db, "task", task_id)
        context = await _build_authorization_context(self.db, self.current_user.id, resource_data)
        
        return AuthorizationService.has_permission(
            self.current_user.id, Permission.ASSIGN_TASK, "task", resource_data, context
        )


def get_authorization_checker(
    current_user: User = Depends(),  # This would be your auth dependency
    db: Session = Depends(get_db)
) -> AuthorizationChecker:
    """Dependency to get authorization checker"""
    return AuthorizationChecker(db, current_user)


# Convenience decorators for common permissions
def require_project_edit(func: Callable) -> Callable:
    """Require project edit permission"""
    return require_permission(Permission.EDIT_PROJECT, "project")(func)


def require_board_delete(func: Callable) -> Callable:
    """Require board delete permission"""
    return require_permission(Permission.DELETE_BOARD, "board")(func)


def require_task_edit(func: Callable) -> Callable:
    """Require task edit permission"""
    return require_permission(Permission.EDIT_TASK, "task")(func)
