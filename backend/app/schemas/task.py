"""
Task schemas for API validation
"""

from pydantic import BaseModel, Field, ConfigDict
from typing import Optional
from datetime import datetime
from app.models.task import TaskStatus, TaskPriority


class TaskBase(BaseModel):
    """Base task schema"""
    title: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = None
    priority: TaskPriority = TaskPriority.MEDIUM
    due_date: Optional[datetime] = None
    board_id: Optional[int] = None


class TaskCreate(TaskBase):
    """Schema for creating a task"""
    assignee_id: Optional[int] = None


class TaskUpdate(BaseModel):
    """Schema for updating a task"""
    title: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = None
    status: Optional[TaskStatus] = None
    priority: Optional[TaskPriority] = None
    due_date: Optional[datetime] = None
    assignee_id: Optional[int] = None
    board_id: Optional[int] = None


class TaskResponse(TaskBase):
    """Schema for task response"""
    model_config = ConfigDict(from_attributes=True)

    id: int
    creator_id: int
    assignee_id: Optional[int]
    status: TaskStatus
    completed_at: Optional[datetime]
    created_at: datetime
    updated_at: datetime


class TaskWithDetails(TaskResponse):
    """Schema for task with creator and assignee details"""
    model_config = ConfigDict(from_attributes=True)

    creator: Optional[dict] = None  # UserProfile
    assignee: Optional[dict] = None  # UserProfile
    board: Optional[dict] = None  # BoardResponse


class TaskStatusUpdate(BaseModel):
    """Schema for updating task status"""
    status: TaskStatus


class TaskAssignmentUpdate(BaseModel):
    """Schema for updating task assignment"""
    assignee_id: Optional[int] = None
