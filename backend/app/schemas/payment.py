"""
Payment and subscription schemas for API validation
"""

from pydantic import BaseModel, Field, ConfigDict
from typing import Optional, Dict, Any, List
from datetime import datetime
from decimal import Decimal
from app.models.payment import PaymentStatus, SubscriptionStatus, SubscriptionPlan


class PaymentBase(BaseModel):
    """Base payment schema"""
    amount: Decimal = Field(..., gt=0, decimal_places=2)
    memo: Optional[str] = None


class PaymentCreate(PaymentBase):
    """Schema for creating a payment"""
    pi_payment_id: str = Field(..., min_length=1)
    payment_metadata: Optional[str] = None


class PaymentUpdate(BaseModel):
    """Schema for updating a payment"""
    status: Optional[PaymentStatus] = None
    txid: Optional[str] = None
    payment_metadata: Optional[str] = None


class PaymentResponse(PaymentBase):
    """Schema for payment response"""
    model_config = ConfigDict(from_attributes=True)

    id: int
    pi_payment_id: str
    txid: Optional[str]
    user_id: int
    status: PaymentStatus
    payment_metadata: Optional[str]
    approved_at: Optional[datetime]
    completed_at: Optional[datetime]
    created_at: datetime
    updated_at: datetime


# Pi Network Payment Callbacks
class PaymentApprovalRequest(BaseModel):
    """Schema for payment approval request"""
    paymentId: str


class PaymentCompletionRequest(BaseModel):
    """Schema for payment completion request"""
    paymentId: str
    txid: str


class PaymentCancellationRequest(BaseModel):
    """Schema for payment cancellation request"""
    paymentId: str


class IncompletePaymentRequest(BaseModel):
    """Schema for incomplete payment handling"""
    payment: Dict[str, Any]


# Subscription Schemas
class SubscriptionBase(BaseModel):
    """Base subscription schema"""
    plan: SubscriptionPlan
    start_date: datetime
    end_date: datetime


class SubscriptionCreate(SubscriptionBase):
    """Schema for creating a subscription"""
    payment_id: Optional[int] = None
    features: Optional[str] = None


class SubscriptionUpdate(BaseModel):
    """Schema for updating a subscription"""
    status: Optional[SubscriptionStatus] = None
    end_date: Optional[datetime] = None
    auto_renew: Optional[bool] = None
    features: Optional[str] = None


class SubscriptionResponse(SubscriptionBase):
    """Schema for subscription response"""
    model_config = ConfigDict(from_attributes=True)

    id: int
    user_id: int
    status: SubscriptionStatus
    payment_id: Optional[int]
    features: Optional[str]
    auto_renew: bool
    created_at: datetime
    updated_at: datetime


class SubscriptionWithPayment(SubscriptionResponse):
    """Schema for subscription with payment details"""
    model_config = ConfigDict(from_attributes=True)

    payment: Optional[PaymentResponse] = None


# Plan Information
class PlanInfo(BaseModel):
    """Schema for subscription plan information"""
    plan: SubscriptionPlan
    name: str
    description: str
    price: Decimal
    features: List[str]
    duration_days: int


class SubscriptionPlansResponse(BaseModel):
    """Schema for available subscription plans"""
    plans: List[PlanInfo]
