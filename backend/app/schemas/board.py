"""
Board schemas for API validation
"""

from pydantic import BaseModel, Field, ConfigDict
from typing import List, Optional
from datetime import datetime


class BoardBase(BaseModel):
    """Base board schema"""
    name: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = None
    is_public: bool = False


class BoardCreate(BoardBase):
    """Schema for creating a board"""
    project_id: Optional[int] = None


class BoardUpdate(BaseModel):
    """Schema for updating a board"""
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = None
    is_public: Optional[bool] = None
    is_archived: Optional[bool] = None
    project_id: Optional[int] = None


class BoardResponse(BoardBase):
    """Schema for board response"""
    model_config = ConfigDict(from_attributes=True)

    id: int
    owner_id: int
    project_id: Optional[int]
    is_archived: bool
    created_at: datetime
    updated_at: datetime


class BoardWithDetails(BoardResponse):
    """Schema for board with owner and member details"""
    model_config = ConfigDict(from_attributes=True)

    owner: Optional[dict] = None  # UserProfile
    members: List[dict] = []  # List of BoardMemberResponse
    task_count: Optional[int] = None


# Board Member Schemas
class BoardMemberBase(BaseModel):
    """Base board member schema"""
    role: str = "member"
    can_edit: bool = True
    can_delete: bool = False
    can_invite: bool = False


class BoardMemberCreate(BaseModel):
    """Schema for adding a board member"""
    user_id: int
    role: str = "member"
    can_edit: bool = True
    can_delete: bool = False
    can_invite: bool = False


class BoardMemberUpdate(BaseModel):
    """Schema for updating board member permissions"""
    role: Optional[str] = None
    can_edit: Optional[bool] = None
    can_delete: Optional[bool] = None
    can_invite: Optional[bool] = None


class BoardMemberResponse(BoardMemberBase):
    """Schema for board member response"""
    model_config = ConfigDict(from_attributes=True)

    id: int
    board_id: int
    user_id: int
    created_at: datetime
    updated_at: datetime


class BoardMemberWithUser(BoardMemberResponse):
    """Schema for board member with user details"""
    model_config = ConfigDict(from_attributes=True)

    user: Optional[dict] = None  # UserProfile


class BoardInviteRequest(BaseModel):
    """Schema for board invitation"""
    user_uid: str  # Pi Network user ID
    role: str = "member"
    can_edit: bool = True
    can_delete: bool = False
    can_invite: bool = False
