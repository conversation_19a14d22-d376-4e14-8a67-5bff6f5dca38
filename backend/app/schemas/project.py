"""
Project schemas for API validation
"""

from pydantic import BaseModel, Field, ConfigDict
from typing import List, Optional
from datetime import datetime
from app.models.project import ProjectRole


class ProjectBase(BaseModel):
    """Base project schema"""
    name: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = None
    is_public: bool = False


class ProjectCreate(ProjectBase):
    """Schema for creating a project"""
    pass


class ProjectUpdate(BaseModel):
    """Schema for updating a project"""
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = None
    is_public: Optional[bool] = None
    is_archived: Optional[bool] = None


class ProjectResponse(ProjectBase):
    """Schema for project response"""
    model_config = ConfigDict(from_attributes=True)
    
    id: int
    owner_id: int
    is_archived: bool
    created_at: datetime
    updated_at: datetime


class ProjectWithDetails(ProjectResponse):
    """Schema for project with owner and member details"""
    model_config = ConfigDict(from_attributes=True)
    
    owner: Optional[dict] = None  # UserProfile
    members: List[dict] = []  # List of ProjectMemberResponse
    board_count: Optional[int] = None
    task_count: Optional[int] = None


# Project Member Schemas
class ProjectMemberBase(BaseModel):
    """Base project member schema"""
    role: ProjectRole = ProjectRole.MEMBER
    can_create_boards: bool = True
    can_edit_project: bool = False
    can_delete_project: bool = False
    can_manage_members: bool = False
    can_view_all_boards: bool = True
    can_create_tasks: bool = True
    can_assign_tasks: bool = True


class ProjectMemberCreate(BaseModel):
    """Schema for adding a project member"""
    user_id: int
    role: ProjectRole = ProjectRole.MEMBER
    can_create_boards: bool = True
    can_edit_project: bool = False
    can_delete_project: bool = False
    can_manage_members: bool = False
    can_view_all_boards: bool = True
    can_create_tasks: bool = True
    can_assign_tasks: bool = True


class ProjectMemberUpdate(BaseModel):
    """Schema for updating project member permissions"""
    role: Optional[ProjectRole] = None
    can_create_boards: Optional[bool] = None
    can_edit_project: Optional[bool] = None
    can_delete_project: Optional[bool] = None
    can_manage_members: Optional[bool] = None
    can_view_all_boards: Optional[bool] = None
    can_create_tasks: Optional[bool] = None
    can_assign_tasks: Optional[bool] = None


class ProjectMemberResponse(ProjectMemberBase):
    """Schema for project member response"""
    model_config = ConfigDict(from_attributes=True)
    
    id: int
    project_id: int
    user_id: int
    created_at: datetime
    updated_at: datetime


class ProjectMemberWithUser(ProjectMemberResponse):
    """Schema for project member with user details"""
    model_config = ConfigDict(from_attributes=True)
    
    user: Optional[dict] = None  # UserProfile


class ProjectInviteRequest(BaseModel):
    """Schema for project invitation"""
    user_uid: str  # Pi Network user ID
    role: ProjectRole = ProjectRole.MEMBER
    can_create_boards: bool = True
    can_edit_project: bool = False
    can_delete_project: bool = False
    can_manage_members: bool = False
    can_view_all_boards: bool = True
    can_create_tasks: bool = True
    can_assign_tasks: bool = True


class UserSearchRequest(BaseModel):
    """Schema for searching users to add to project"""
    query: str = Field(..., min_length=1, max_length=100)


class UserSearchResponse(BaseModel):
    """Schema for user search results"""
    users: List[dict] = []  # List of UserProfile
    total: int = 0
