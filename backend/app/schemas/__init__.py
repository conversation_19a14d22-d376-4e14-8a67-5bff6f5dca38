"""
Pydantic schemas for API validation
"""

from .user import (
    UserBase, UserCreate, UserUpdate, UserResponse, UserProfile,
    PiAuthResult, PiSignInRequest
)
from .task import (
    TaskBase, TaskCreate, TaskUpdate, TaskResponse, TaskWithDetails,
    TaskStatusUpdate, TaskAssignmentUpdate
)
from .board import (
    BoardBase, BoardCreate, BoardUpdate, BoardResponse, BoardWithDetails,
    BoardMemberBase, BoardMemberCreate, BoardMemberUpdate, BoardMemberResponse,
    BoardMemberWithUser, BoardInviteRequest
)
from .project import (
    ProjectBase, ProjectCreate, ProjectUpdate, ProjectResponse, ProjectWithDetails,
    ProjectMemberBase, ProjectMemberCreate, ProjectMemberUpdate, ProjectMemberResponse,
    ProjectMemberWithUser, ProjectInviteRequest, UserSearchRequest, UserSearchResponse
)
from .payment import (
    PaymentBase, PaymentCreate, PaymentUpdate, PaymentResponse,
    PaymentApprovalRequest, PaymentCompletionRequest, PaymentCancellationRequest,
    IncompletePaymentRequest, SubscriptionBase, SubscriptionCreate,
    SubscriptionUpdate, SubscriptionResponse, SubscriptionWithPayment,
    PlanInfo, SubscriptionPlansResponse
)

__all__ = [
    # User schemas
    "UserBase", "UserCreate", "UserUpdate", "UserResponse", "UserProfile",
    "PiAuthResult", "PiSignInRequest",

    # Task schemas
    "TaskBase", "TaskCreate", "TaskUpdate", "TaskResponse", "TaskWithDetails",
    "TaskStatusUpdate", "TaskAssignmentUpdate",

    # Board schemas
    "BoardBase", "BoardCreate", "BoardUpdate", "BoardResponse", "BoardWithDetails",
    "BoardMemberBase", "BoardMemberCreate", "BoardMemberUpdate", "BoardMemberResponse",
    "BoardMemberWithUser", "BoardInviteRequest",

    # Project schemas
    "ProjectBase", "ProjectCreate", "ProjectUpdate", "ProjectResponse", "ProjectWithDetails",
    "ProjectMemberBase", "ProjectMemberCreate", "ProjectMemberUpdate", "ProjectMemberResponse",
    "ProjectMemberWithUser", "ProjectInviteRequest", "UserSearchRequest", "UserSearchResponse",

    # Payment schemas
    "PaymentBase", "PaymentCreate", "PaymentUpdate", "PaymentResponse",
    "PaymentApprovalRequest", "PaymentCompletionRequest", "PaymentCancellationRequest",
    "IncompletePaymentRequest", "SubscriptionBase", "SubscriptionCreate",
    "SubscriptionUpdate", "SubscriptionResponse", "SubscriptionWithPayment",
    "PlanInfo", "SubscriptionPlansResponse",
]