"""
User schemas for API validation
"""

from sqlmodel import SQLModel, Field
from pydantic import BaseModel
from typing import List, Optional
from datetime import datetime


class UserBase(SQLModel):
    """Base user schema"""
    username: str = Field(min_length=1, max_length=255)
    roles: List[str] = Field(default_factory=list)


class UserCreate(UserBase):
    """Schema for creating a user"""
    uid: str = Field(min_length=1, max_length=255)
    access_token: str


class UserUpdate(SQLModel):
    """Schema for updating a user"""
    username: Optional[str] = Field(None, min_length=1, max_length=255)
    roles: Optional[List[str]] = None
    access_token: Optional[str] = None
    is_active: Optional[bool] = None
    is_verified: Optional[bool] = None


class UserResponse(UserBase):
    """Schema for user response"""
    id: int
    uid: str
    is_active: bool
    is_verified: bool
    created_at: datetime
    updated_at: datetime


class UserProfile(SQLModel):
    """Schema for user profile (public information)"""
    id: int
    username: str
    roles: List[str]
    is_active: bool
    created_at: datetime


class UserStats(SQLModel):
    """Schema for user statistics"""
    tasks_completed: int
    tasks_in_progress: int
    boards_created: int
    projects_owned: int
    team_members: int


# Pi Network Authentication Schemas
class PiAuthResult(BaseModel):
    """Pi Network authentication result"""
    accessToken: str
    user: dict  # Contains uid, username, roles


class PiSignInRequest(BaseModel):
    """Pi Network sign-in request"""
    authResult: PiAuthResult
