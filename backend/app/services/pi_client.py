"""
Pi Platform API client for authentication and payments
"""

import httpx
import logging
from typing import Optional, Dict, Any
from app.core.config import settings

logger = logging.getLogger(__name__)


class PiPlatformClient:
    """Client for Pi Platform API interactions"""
    
    def __init__(self):
        self.base_url = settings.PI_PLATFORM_API_URL
        self.api_key = settings.PI_API_KEY
        self.timeout = 30.0
        
    async def verify_user(self, access_token: str) -> Optional[Dict[str, Any]]:
        """
        Verify user access token with Pi Platform API
        
        Args:
            access_token: Pi Network access token
            
        Returns:
            User data if token is valid, None otherwise
        """
        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.get(
                    f"{self.base_url}/v2/me",
                    headers={"Authorization": f"Bearer {access_token}"}
                )
                
                if response.status_code == 200:
                    user_data = response.json()
                    logger.info(f"User verified: {user_data.get('uid')}")
                    return user_data
                else:
                    logger.warning(f"Token verification failed: {response.status_code}")
                    return None
                    
        except httpx.TimeoutException:
            logger.error("Pi Platform API timeout during user verification")
            return None
        except Exception as e:
            logger.error(f"Error verifying user token: {e}")
            return None
    
    async def get_payment(self, payment_id: str) -> Optional[Dict[str, Any]]:
        """
        Get payment details from Pi Platform
        
        Args:
            payment_id: Pi payment identifier
            
        Returns:
            Payment data if found, None otherwise
        """
        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.get(
                    f"{self.base_url}/v2/payments/{payment_id}",
                    headers={"Authorization": f"Key {self.api_key}"}
                )
                
                if response.status_code == 200:
                    payment_data = response.json()
                    logger.info(f"Payment retrieved: {payment_id}")
                    return payment_data
                else:
                    logger.warning(f"Payment retrieval failed: {response.status_code}")
                    return None
                    
        except httpx.TimeoutException:
            logger.error("Pi Platform API timeout during payment retrieval")
            return None
        except Exception as e:
            logger.error(f"Error retrieving payment: {e}")
            return None
    
    async def approve_payment(self, payment_id: str) -> bool:
        """
        Approve payment with Pi Platform
        
        Args:
            payment_id: Pi payment identifier
            
        Returns:
            True if approval successful, False otherwise
        """
        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.post(
                    f"{self.base_url}/v2/payments/{payment_id}/approve",
                    headers={"Authorization": f"Key {self.api_key}"}
                )
                
                if response.status_code == 200:
                    logger.info(f"Payment approved: {payment_id}")
                    return True
                else:
                    logger.warning(f"Payment approval failed: {response.status_code}")
                    return False
                    
        except httpx.TimeoutException:
            logger.error("Pi Platform API timeout during payment approval")
            return False
        except Exception as e:
            logger.error(f"Error approving payment: {e}")
            return False
    
    async def complete_payment(self, payment_id: str, txid: str) -> bool:
        """
        Complete payment with Pi Platform
        
        Args:
            payment_id: Pi payment identifier
            txid: Blockchain transaction ID
            
        Returns:
            True if completion successful, False otherwise
        """
        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.post(
                    f"{self.base_url}/v2/payments/{payment_id}/complete",
                    headers={"Authorization": f"Key {self.api_key}"},
                    json={"txid": txid}
                )
                
                if response.status_code == 200:
                    logger.info(f"Payment completed: {payment_id}")
                    return True
                else:
                    logger.warning(f"Payment completion failed: {response.status_code}")
                    return False
                    
        except httpx.TimeoutException:
            logger.error("Pi Platform API timeout during payment completion")
            return False
        except Exception as e:
            logger.error(f"Error completing payment: {e}")
            return False
    
    async def cancel_payment(self, payment_id: str) -> bool:
        """
        Cancel payment with Pi Platform
        
        Args:
            payment_id: Pi payment identifier
            
        Returns:
            True if cancellation successful, False otherwise
        """
        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.post(
                    f"{self.base_url}/v2/payments/{payment_id}/cancel",
                    headers={"Authorization": f"Key {self.api_key}"}
                )
                
                if response.status_code == 200:
                    logger.info(f"Payment cancelled: {payment_id}")
                    return True
                else:
                    logger.warning(f"Payment cancellation failed: {response.status_code}")
                    return False
                    
        except httpx.TimeoutException:
            logger.error("Pi Platform API timeout during payment cancellation")
            return False
        except Exception as e:
            logger.error(f"Error cancelling payment: {e}")
            return False


# Global Pi client instance
pi_client = PiPlatformClient()
