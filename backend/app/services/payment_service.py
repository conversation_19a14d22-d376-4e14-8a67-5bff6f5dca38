"""
Payment and subscription service for Pi Network integration
"""

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
from decimal import Decimal
import json
import logging
import httpx

from app.models.payment import Payment, Subscription, PaymentStatus, SubscriptionStatus, SubscriptionPlan
from app.models.user import User
from app.schemas.payment import (
    PaymentCreate, PaymentResponse, SubscriptionCreate, SubscriptionResponse,
    PlanInfo, SubscriptionPlansResponse
)
from app.services.pi_client import pi_client
from app.database.utils import DatabaseUtils

logger = logging.getLogger(__name__)


class PaymentService:
    """Service for payment and subscription operations"""
    
    # Subscription plan configurations
    SUBSCRIPTION_PLANS = {
        SubscriptionPlan.FREE_TRIAL: {
            "name": "Free Trial",
            "description": "1 month free access to basic features",
            "price": Decimal("0.00"),
            "features": ["Basic task management", "Up to 3 boards", "5 team members"],
            "duration_days": 30
        },
        SubscriptionPlan.BASIC: {
            "name": "Basic Plan",
            "description": "Essential features for small teams",
            "price": Decimal("5.00"),
            "features": ["Task management", "Up to 10 boards", "15 team members", "Basic reporting"],
            "duration_days": 30
        },
        SubscriptionPlan.STANDARD: {
            "name": "Standard Plan", 
            "description": "Advanced features for growing teams",
            "price": Decimal("15.00"),
            "features": ["Advanced task management", "Unlimited boards", "50 team members", "Advanced reporting", "Integrations"],
            "duration_days": 30
        },
        SubscriptionPlan.PREMIUM: {
            "name": "Premium Plan",
            "description": "Full features for large organizations",
            "price": Decimal("25.00"),
            "features": ["All features", "Unlimited everything", "Priority support", "Custom integrations", "Advanced analytics"],
            "duration_days": 30
        }
    }
    
    @staticmethod
    async def get_subscription_plans() -> SubscriptionPlansResponse:
        """
        Get available subscription plans
        
        Returns:
            Available subscription plans
        """
        plans = []
        for plan_type, config in PaymentService.SUBSCRIPTION_PLANS.items():
            plan_info = PlanInfo(
                plan=plan_type,
                name=config["name"],
                description=config["description"],
                price=config["price"],
                features=config["features"],
                duration_days=config["duration_days"]
            )
            plans.append(plan_info)
        
        return SubscriptionPlansResponse(plans=plans)
    
    @staticmethod
    async def create_payment(
        db: AsyncSession,
        payment_data: PaymentCreate,
        user: User
    ) -> PaymentResponse:
        """
        Create a new payment record
        
        Args:
            db: Database session
            payment_data: Payment creation data
            user: User making the payment
            
        Returns:
            Created payment
        """
        try:
            # Create payment record
            payment = await DatabaseUtils.create(
                db, Payment,
                pi_payment_id=payment_data.pi_payment_id,
                user_id=user.id,
                amount=payment_data.amount,
                memo=payment_data.memo,
                metadata=payment_data.metadata,
                status=PaymentStatus.PENDING
            )
            
            logger.info(f"Payment created: {payment.pi_payment_id} for user {user.uid}")
            return PaymentResponse.model_validate(payment)
            
        except Exception as e:
            logger.error(f"Error creating payment: {e}")
            raise
    
    @staticmethod
    async def approve_payment(
        db: AsyncSession,
        payment_id: str,
        user: User
    ) -> bool:
        """
        Approve payment with Pi Platform
        
        Args:
            db: Database session
            payment_id: Pi payment identifier
            user: User approving the payment
            
        Returns:
            True if approval successful, False otherwise
        """
        try:
            # Get payment from database
            payment = await DatabaseUtils.get_by_field(db, Payment, "pi_payment_id", payment_id)
            if not payment or payment.user_id != user.id:
                logger.warning(f"Payment not found or unauthorized: {payment_id}")
                return False
            
            if payment.status != PaymentStatus.PENDING:
                logger.warning(f"Payment not in pending status: {payment_id}")
                return False
            
            # Get payment details from Pi Platform
            payment_info = await pi_client.get_payment(payment_id)
            if not payment_info:
                logger.error(f"Failed to get payment info from Pi Platform: {payment_id}")
                return False
            
            # Approve payment with Pi Platform
            success = await pi_client.approve_payment(payment_id)
            if not success:
                logger.error(f"Failed to approve payment with Pi Platform: {payment_id}")
                return False
            
            # Update payment status
            await DatabaseUtils.update(
                db, payment,
                status=PaymentStatus.APPROVED,
                approved_at=datetime.utcnow()
            )
            
            logger.info(f"Payment approved: {payment_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error approving payment {payment_id}: {e}")
            return False
    
    @staticmethod
    async def complete_payment(
        db: AsyncSession,
        payment_id: str,
        txid: str
    ) -> bool:
        """
        Complete payment with Pi Platform
        
        Args:
            db: Database session
            payment_id: Pi payment identifier
            txid: Blockchain transaction ID
            
        Returns:
            True if completion successful, False otherwise
        """
        try:
            # Get payment from database
            payment = await DatabaseUtils.get_by_field(db, Payment, "pi_payment_id", payment_id)
            if not payment:
                logger.warning(f"Payment not found: {payment_id}")
                return False
            
            if payment.status != PaymentStatus.APPROVED:
                logger.warning(f"Payment not in approved status: {payment_id}")
                return False
            
            # Complete payment with Pi Platform
            success = await pi_client.complete_payment(payment_id, txid)
            if not success:
                logger.error(f"Failed to complete payment with Pi Platform: {payment_id}")
                return False
            
            # Update payment status
            await DatabaseUtils.update(
                db, payment,
                status=PaymentStatus.COMPLETED,
                txid=txid,
                completed_at=datetime.utcnow()
            )
            
            # Process subscription if this is a subscription payment
            await PaymentService._process_subscription_payment(db, payment)
            
            logger.info(f"Payment completed: {payment_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error completing payment {payment_id}: {e}")
            return False
    
    @staticmethod
    async def cancel_payment(
        db: AsyncSession,
        payment_id: str
    ) -> bool:
        """
        Cancel payment
        
        Args:
            db: Database session
            payment_id: Pi payment identifier
            
        Returns:
            True if cancellation successful, False otherwise
        """
        try:
            # Get payment from database
            payment = await DatabaseUtils.get_by_field(db, Payment, "pi_payment_id", payment_id)
            if not payment:
                logger.warning(f"Payment not found: {payment_id}")
                return False
            
            # Cancel payment with Pi Platform
            success = await pi_client.cancel_payment(payment_id)
            if not success:
                logger.error(f"Failed to cancel payment with Pi Platform: {payment_id}")
                return False
            
            # Update payment status
            await DatabaseUtils.update(db, payment, status=PaymentStatus.CANCELLED)
            
            logger.info(f"Payment cancelled: {payment_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error cancelling payment {payment_id}: {e}")
            return False
    
    @staticmethod
    async def handle_incomplete_payment(
        db: AsyncSession,
        payment_data: Dict[str, Any]
    ) -> bool:
        """
        Handle incomplete payment from Pi Platform
        
        Args:
            db: Database session
            payment_data: Payment data from Pi Platform
            
        Returns:
            True if handling successful, False otherwise
        """
        try:
            payment_id = payment_data["identifier"]
            txid = payment_data.get("transaction", {}).get("txid")
            tx_url = payment_data.get("transaction", {}).get("_link")
            
            if not txid or not tx_url:
                logger.warning(f"Incomplete payment missing transaction data: {payment_id}")
                return False
            
            # Get payment from database
            payment = await DatabaseUtils.get_by_field(db, Payment, "pi_payment_id", payment_id)
            if not payment:
                logger.warning(f"Payment not found for incomplete payment: {payment_id}")
                return False
            
            # Verify transaction on Pi blockchain
            async with httpx.AsyncClient() as client:
                response = await client.get(tx_url)
                if response.status_code != 200:
                    logger.error(f"Failed to verify transaction: {tx_url}")
                    return False
                
                blockchain_data = response.json()
                payment_id_on_block = blockchain_data.get("memo")
                
                if payment_id_on_block != payment_id:
                    logger.error(f"Payment ID mismatch on blockchain: {payment_id}")
                    return False
            
            # Complete the payment
            return await PaymentService.complete_payment(db, payment_id, txid)
            
        except Exception as e:
            logger.error(f"Error handling incomplete payment: {e}")
            return False
    
    @staticmethod
    async def _process_subscription_payment(db: AsyncSession, payment: Payment):
        """Process subscription after successful payment"""
        try:
            if not payment.metadata:
                return
            
            metadata = json.loads(payment.metadata)
            plan_type = metadata.get("subscription_plan")
            
            if not plan_type:
                return
            
            # Get plan configuration
            plan_config = PaymentService.SUBSCRIPTION_PLANS.get(SubscriptionPlan(plan_type))
            if not plan_config:
                logger.error(f"Invalid subscription plan: {plan_type}")
                return
            
            # Create subscription
            start_date = datetime.utcnow()
            end_date = start_date + timedelta(days=plan_config["duration_days"])
            
            subscription = await DatabaseUtils.create(
                db, Subscription,
                user_id=payment.user_id,
                plan=SubscriptionPlan(plan_type),
                status=SubscriptionStatus.ACTIVE,
                start_date=start_date,
                end_date=end_date,
                payment_id=payment.id,
                features=json.dumps(plan_config["features"])
            )
            
            logger.info(f"Subscription created: {subscription.id} for user {payment.user_id}")
            
        except Exception as e:
            logger.error(f"Error processing subscription payment: {e}")
    
    @staticmethod
    async def get_user_subscriptions(
        db: AsyncSession,
        user: User
    ) -> List[SubscriptionResponse]:
        """
        Get user's subscriptions
        
        Args:
            db: Database session
            user: User
            
        Returns:
            List of user subscriptions
        """
        try:
            subscriptions = await DatabaseUtils.get_all(
                db, Subscription,
                filters={"user_id": user.id}
            )
            return [SubscriptionResponse.model_validate(sub) for sub in subscriptions]
            
        except Exception as e:
            logger.error(f"Error getting user subscriptions: {e}")
            return []
    
    @staticmethod
    async def get_active_subscription(
        db: AsyncSession,
        user: User
    ) -> Optional[SubscriptionResponse]:
        """
        Get user's active subscription
        
        Args:
            db: Database session
            user: User
            
        Returns:
            Active subscription if found, None otherwise
        """
        try:
            result = await db.execute(
                select(Subscription).where(
                    Subscription.user_id == user.id,
                    Subscription.status == SubscriptionStatus.ACTIVE,
                    Subscription.end_date > datetime.utcnow()
                ).order_by(Subscription.end_date.desc())
            )
            subscription = result.scalar_one_or_none()
            
            if subscription:
                return SubscriptionResponse.model_validate(subscription)
            return None
            
        except Exception as e:
            logger.error(f"Error getting active subscription: {e}")
            return None


# Global payment service instance
payment_service = PaymentService()
