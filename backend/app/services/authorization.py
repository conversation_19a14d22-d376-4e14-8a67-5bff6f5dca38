"""
Authorization service for ownership-based permissions
"""

from typing import Optional, Dict, Any
from enum import Enum


class Permission(str, Enum):
    """Permission types for the authorization system"""
    # Project permissions
    DELETE_PROJECT = "delete_project"
    EDIT_PROJECT = "edit_project"
    MANAGE_PROJECT_MEMBERS = "manage_project_members"
    
    # Board permissions
    CREATE_BOARD = "create_board"
    DELETE_BOARD = "delete_board"
    EDIT_BOARD = "edit_board"
    MANAGE_BOARD_MEMBERS = "manage_board_members"
    
    # Task permissions
    CREATE_TASK = "create_task"
    DELETE_TASK = "delete_task"
    EDIT_TASK = "edit_task"
    ASSIGN_TASK = "assign_task"
    UPDATE_TASK_STATUS = "update_task_status"
    
    # General permissions
    VIEW_CONTENT = "view_content"
    COMMENT = "comment"


class UserType(str, Enum):
    """User types based on ownership hierarchy"""
    PROJECT_OWNER = "project_owner"
    PROJECT_MANAGER = "project_manager"
    BOARD_OWNER = "board_owner"
    TASK_CREATOR = "task_creator"
    ASSIGNEE = "assignee"
    MEMBER = "member"
    VIEWER = "viewer"


class AuthorizationService:
    """Service for handling ownership-based authorization"""
    
    @staticmethod
    def has_permission(
        user_id: int,
        permission: Permission,
        resource_type: str,
        resource: Dict[str, Any],
        context: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        Check if user has permission for a specific resource
        
        Args:
            user_id: ID of the user requesting permission
            permission: The permission being requested
            resource_type: Type of resource (project, board, task)
            resource: The resource data
            context: Additional context (project memberships, etc.)
        """
        user_type = AuthorizationService._get_user_type(user_id, resource_type, resource, context)
        
        return AuthorizationService._check_permission(permission, user_type, resource_type, resource)
    
    @staticmethod
    def _get_user_type(
        user_id: int,
        resource_type: str,
        resource: Dict[str, Any],
        context: Optional[Dict[str, Any]] = None
    ) -> UserType:
        """Determine user type based on ownership hierarchy"""
        
        if resource_type == "project":
            # Check if user is project owner
            if resource.get("created_by") == user_id:
                return UserType.PROJECT_OWNER
            
            # Check if user is project manager
            if context and context.get("project_memberships"):
                for membership in context["project_memberships"]:
                    if (membership.get("user_id") == user_id and 
                        membership.get("project_id") == resource.get("id") and
                        membership.get("is_manager")):
                        return UserType.PROJECT_MANAGER
            
            # Check if user is project member
            if context and context.get("project_memberships"):
                for membership in context["project_memberships"]:
                    if (membership.get("user_id") == user_id and 
                        membership.get("project_id") == resource.get("id")):
                        return UserType.MEMBER
        
        elif resource_type == "board":
            # Check if user is board owner
            if resource.get("created_by") == user_id:
                return UserType.BOARD_OWNER
            
            # Check if user is project owner (if board belongs to project)
            if resource.get("project") and resource["project"].get("created_by") == user_id:
                return UserType.PROJECT_OWNER
            
            # Check if user is project manager
            if (resource.get("project") and context and context.get("project_memberships")):
                for membership in context["project_memberships"]:
                    if (membership.get("user_id") == user_id and 
                        membership.get("project_id") == resource["project"].get("id") and
                        membership.get("is_manager")):
                        return UserType.PROJECT_MANAGER
        
        elif resource_type == "task":
            # Check if user is task creator
            if resource.get("created_by") == user_id:
                return UserType.TASK_CREATOR
            
            # Check if user is task assignee
            if resource.get("assignee_id") == user_id:
                return UserType.ASSIGNEE
            
            # Check if user is board owner
            if resource.get("board") and resource["board"].get("created_by") == user_id:
                return UserType.BOARD_OWNER
            
            # Check if user is project owner (via board)
            if (resource.get("board") and resource["board"].get("project") and 
                resource["board"]["project"].get("created_by") == user_id):
                return UserType.PROJECT_OWNER
        
        return UserType.VIEWER
    
    @staticmethod
    def _check_permission(
        permission: Permission,
        user_type: UserType,
        resource_type: str,
        resource: Dict[str, Any]
    ) -> bool:
        """Check if user type has specific permission"""
        
        # Project Owner has all permissions
        if user_type == UserType.PROJECT_OWNER:
            return True
        
        # Permission-specific logic
        if permission == Permission.DELETE_PROJECT:
            return user_type == UserType.PROJECT_OWNER
        
        elif permission == Permission.EDIT_PROJECT:
            return user_type in [UserType.PROJECT_OWNER, UserType.PROJECT_MANAGER]
        
        elif permission == Permission.MANAGE_PROJECT_MEMBERS:
            return user_type == UserType.PROJECT_OWNER
        
        elif permission == Permission.CREATE_BOARD:
            return user_type in [
                UserType.PROJECT_OWNER, 
                UserType.PROJECT_MANAGER, 
                UserType.MEMBER
            ]
        
        elif permission == Permission.DELETE_BOARD:
            return user_type in [UserType.PROJECT_OWNER, UserType.BOARD_OWNER]
        
        elif permission == Permission.EDIT_BOARD:
            return user_type in [
                UserType.PROJECT_OWNER, 
                UserType.PROJECT_MANAGER, 
                UserType.BOARD_OWNER
            ]
        
        elif permission == Permission.MANAGE_BOARD_MEMBERS:
            return user_type in [
                UserType.PROJECT_OWNER, 
                UserType.PROJECT_MANAGER, 
                UserType.BOARD_OWNER
            ]
        
        elif permission == Permission.CREATE_TASK:
            return user_type in [
                UserType.PROJECT_OWNER, 
                UserType.PROJECT_MANAGER, 
                UserType.BOARD_OWNER, 
                UserType.MEMBER
            ]
        
        elif permission == Permission.DELETE_TASK:
            return user_type in [
                UserType.PROJECT_OWNER, 
                UserType.PROJECT_MANAGER, 
                UserType.BOARD_OWNER, 
                UserType.TASK_CREATOR
            ]
        
        elif permission == Permission.EDIT_TASK:
            return user_type in [
                UserType.PROJECT_OWNER, 
                UserType.PROJECT_MANAGER, 
                UserType.BOARD_OWNER, 
                UserType.TASK_CREATOR, 
                UserType.ASSIGNEE
            ]
        
        elif permission == Permission.ASSIGN_TASK:
            return user_type in [
                UserType.PROJECT_OWNER, 
                UserType.PROJECT_MANAGER, 
                UserType.BOARD_OWNER
            ]
        
        elif permission == Permission.UPDATE_TASK_STATUS:
            return user_type in [
                UserType.PROJECT_OWNER, 
                UserType.PROJECT_MANAGER, 
                UserType.BOARD_OWNER, 
                UserType.TASK_CREATOR, 
                UserType.ASSIGNEE
            ]
        
        elif permission == Permission.VIEW_CONTENT:
            # Most users can view content they have access to
            return user_type != UserType.VIEWER or resource.get("is_public", False)
        
        elif permission == Permission.COMMENT:
            return user_type in [
                UserType.PROJECT_OWNER, 
                UserType.PROJECT_MANAGER, 
                UserType.BOARD_OWNER, 
                UserType.TASK_CREATOR, 
                UserType.ASSIGNEE, 
                UserType.MEMBER
            ]
        
        return False
    
    @staticmethod
    def get_user_permissions(
        user_id: int,
        resource_type: str,
        resource: Dict[str, Any],
        context: Optional[Dict[str, Any]] = None
    ) -> list[Permission]:
        """Get all permissions for a user on a specific resource"""
        user_type = AuthorizationService._get_user_type(user_id, resource_type, resource, context)
        permissions = []
        
        for permission in Permission:
            if AuthorizationService._check_permission(permission, user_type, resource_type, resource):
                permissions.append(permission)
        
        return permissions


# Convenience functions for common authorization checks
def can_edit_project(user_id: int, project: Dict[str, Any], context: Optional[Dict[str, Any]] = None) -> bool:
    return AuthorizationService.has_permission(
        user_id, Permission.EDIT_PROJECT, "project", project, context
    )


def can_delete_board(user_id: int, board: Dict[str, Any], context: Optional[Dict[str, Any]] = None) -> bool:
    return AuthorizationService.has_permission(
        user_id, Permission.DELETE_BOARD, "board", board, context
    )


def can_update_task(user_id: int, task: Dict[str, Any], context: Optional[Dict[str, Any]] = None) -> bool:
    return AuthorizationService.has_permission(
        user_id, Permission.EDIT_TASK, "task", task, context
    )
