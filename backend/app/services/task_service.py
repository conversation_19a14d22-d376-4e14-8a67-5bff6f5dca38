"""
Task management service
"""

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_
from sqlalchemy.orm import selectinload
from typing import List, Optional, Dict, Any
from datetime import datetime
import logging

from app.models.task import Task, TaskStatus, TaskPriority
from app.models.user import User
from app.models.board import Board
from app.models.project import Project, ProjectMember
from app.schemas.task import TaskCreate, TaskUpdate, TaskResponse, TaskWithDetails
from app.database.utils import DatabaseUtils

logger = logging.getLogger(__name__)


class TaskService:
    """Service for task management operations"""
    
    @staticmethod
    async def create_task(
        db: AsyncSession,
        task_data: TaskCreate,
        creator: User
    ) -> TaskResponse:
        """
        Create a new task
        
        Args:
            db: Database session
            task_data: Task creation data
            creator: User creating the task
            
        Returns:
            Created task
        """
        try:
            # Validate assignee if provided
            if task_data.assignee_id:
                assignee = await DatabaseUtils.get_by_id(db, User, task_data.assignee_id)
                if not assignee or not assignee.is_active:
                    raise ValueError("Invalid assignee")
            
            # Validate board if provided
            if task_data.board_id:
                board = await DatabaseUtils.get_by_id(db, Board, task_data.board_id)
                if not board:
                    raise ValueError("Invalid board")
                # TODO: Check if user has permission to add tasks to this board
            
            # Create task
            task = await DatabaseUtils.create(
                db, Task,
                title=task_data.title,
                description=task_data.description,
                creator_id=creator.id,
                assignee_id=task_data.assignee_id,
                priority=task_data.priority,
                due_date=task_data.due_date,
                board_id=task_data.board_id,
                status=TaskStatus.TODO
            )
            
            logger.info(f"Task created: {task.id} by user {creator.uid}")
            return TaskResponse.model_validate(task)
            
        except ValueError as e:
            logger.warning(f"Task creation validation error: {e}")
            raise
        except Exception as e:
            logger.error(f"Error creating task: {e}")
            raise
    
    @staticmethod
    async def get_task(
        db: AsyncSession,
        task_id: int,
        include_details: bool = False
    ) -> Optional[TaskResponse]:
        """
        Get task by ID
        
        Args:
            db: Database session
            task_id: Task ID
            include_details: Whether to include creator/assignee details
            
        Returns:
            Task if found, None otherwise
        """
        try:
            if include_details:
                result = await db.execute(
                    select(Task)
                    .options(
                        selectinload(Task.creator),
                        selectinload(Task.assignee),
                        selectinload(Task.board)
                    )
                    .where(Task.id == task_id)
                )
                task = result.scalar_one_or_none()
                if task:
                    return TaskWithDetails.model_validate(task)
            else:
                task = await DatabaseUtils.get_by_id(db, Task, task_id)
                if task:
                    return TaskResponse.model_validate(task)
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting task {task_id}: {e}")
            return None
    
    @staticmethod
    async def update_task(
        db: AsyncSession,
        task_id: int,
        task_data: TaskUpdate,
        user: User
    ) -> Optional[TaskResponse]:
        """
        Update task
        
        Args:
            db: Database session
            task_id: Task ID
            task_data: Task update data
            user: User performing the update
            
        Returns:
            Updated task if successful, None otherwise
        """
        try:
            task = await DatabaseUtils.get_by_id(db, Task, task_id)
            if not task:
                return None
            
            # Check permissions (creator or assignee can update)
            if task.creator_id != user.id and task.assignee_id != user.id:
                # TODO: Check board permissions
                raise PermissionError("Insufficient permissions to update task")
            
            # Validate assignee if being updated
            if task_data.assignee_id is not None:
                if task_data.assignee_id:
                    assignee = await DatabaseUtils.get_by_id(db, User, task_data.assignee_id)
                    if not assignee or not assignee.is_active:
                        raise ValueError("Invalid assignee")
            
            # Validate board if being updated
            if task_data.board_id is not None:
                if task_data.board_id:
                    board = await DatabaseUtils.get_by_id(db, Board, task_data.board_id)
                    if not board:
                        raise ValueError("Invalid board")
            
            # Update task fields
            update_data = {}
            for field, value in task_data.dict(exclude_unset=True).items():
                if value is not None:
                    update_data[field] = value
            
            # Set completion timestamp if status changed to completed
            if task_data.status == TaskStatus.COMPLETED and task.status != TaskStatus.COMPLETED:
                update_data["completed_at"] = datetime.utcnow()
            elif task_data.status and task_data.status != TaskStatus.COMPLETED:
                update_data["completed_at"] = None
            
            updated_task = await DatabaseUtils.update(db, task, **update_data)
            logger.info(f"Task updated: {task_id} by user {user.uid}")
            return TaskResponse.model_validate(updated_task)
            
        except (ValueError, PermissionError) as e:
            logger.warning(f"Task update error: {e}")
            raise
        except Exception as e:
            logger.error(f"Error updating task {task_id}: {e}")
            return None
    
    @staticmethod
    async def delete_task(
        db: AsyncSession,
        task_id: int,
        user: User
    ) -> bool:
        """
        Delete task
        
        Args:
            db: Database session
            task_id: Task ID
            user: User performing the deletion
            
        Returns:
            True if deletion successful, False otherwise
        """
        try:
            task = await DatabaseUtils.get_by_id(db, Task, task_id)
            if not task:
                return False
            
            # Check permissions (only creator can delete)
            if task.creator_id != user.id:
                # TODO: Check board admin permissions
                raise PermissionError("Insufficient permissions to delete task")
            
            success = await DatabaseUtils.delete(db, task)
            if success:
                logger.info(f"Task deleted: {task_id} by user {user.uid}")
            return success
            
        except PermissionError as e:
            logger.warning(f"Task deletion permission error: {e}")
            raise
        except Exception as e:
            logger.error(f"Error deleting task {task_id}: {e}")
            return False
    
    @staticmethod
    async def list_tasks(
        db: AsyncSession,
        user: User,
        skip: int = 0,
        limit: int = 100,
        status: Optional[TaskStatus] = None,
        priority: Optional[TaskPriority] = None,
        assignee_id: Optional[int] = None,
        board_id: Optional[int] = None,
        created_by_me: bool = False,
        assigned_to_me: bool = False,
        include_details: bool = False
    ) -> List[TaskResponse]:
        """
        List tasks with filtering
        
        Args:
            db: Database session
            user: Current user
            skip: Number of records to skip
            limit: Maximum number of records to return
            status: Filter by task status
            priority: Filter by task priority
            assignee_id: Filter by assignee
            board_id: Filter by board
            created_by_me: Filter tasks created by current user
            assigned_to_me: Filter tasks assigned to current user
            include_details: Whether to include creator/assignee details
            
        Returns:
            List of tasks
        """
        try:
            query = select(Task)
            
            # Add joins for details if requested
            if include_details:
                query = query.options(
                    selectinload(Task.creator),
                    selectinload(Task.assignee),
                    selectinload(Task.board)
                )
            
            # Build filters
            filters = []
            
            # User-specific filters
            if created_by_me:
                filters.append(Task.creator_id == user.id)
            if assigned_to_me:
                filters.append(Task.assignee_id == user.id)
            
            # If no user-specific filters, show tasks user has access to
            if not created_by_me and not assigned_to_me:
                filters.append(
                    or_(
                        Task.creator_id == user.id,
                        Task.assignee_id == user.id,
                        # TODO: Add board membership check
                    )
                )
            
            # Other filters
            if status:
                filters.append(Task.status == status)
            if priority:
                filters.append(Task.priority == priority)
            if assignee_id:
                filters.append(Task.assignee_id == assignee_id)
            if board_id:
                filters.append(Task.board_id == board_id)
            
            # Apply filters
            if filters:
                query = query.where(and_(*filters))
            
            # Apply pagination
            query = query.offset(skip).limit(limit).order_by(Task.created_at.desc())
            
            result = await db.execute(query)
            tasks = result.scalars().all()
            
            if include_details:
                return [TaskWithDetails.model_validate(task) for task in tasks]
            else:
                return [TaskResponse.model_validate(task) for task in tasks]
            
        except Exception as e:
            logger.error(f"Error listing tasks: {e}")
            return []


# Global task service instance
task_service = TaskService()
