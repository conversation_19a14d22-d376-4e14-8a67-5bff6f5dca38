"""
Board collaboration service
"""

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, func
from sqlalchemy.orm import selectinload
from typing import List, Optional, Dict, Any
import logging

from app.models.board import Board, BoardMember
from app.models.user import User
from app.models.task import Task
from app.models.project import Project, ProjectMember
from app.schemas.board import (
    BoardCreate, BoardUpdate, BoardResponse, BoardWithDetails,
    BoardMemberCreate, BoardMemberUpdate, BoardMemberResponse,
    BoardMemberWithUser, BoardInviteRequest
)
from app.database.utils import DatabaseUtils

logger = logging.getLogger(__name__)


class BoardService:
    """Service for board collaboration operations"""
    
    @staticmethod
    async def create_board(
        db: AsyncSession,
        board_data: BoardCreate,
        owner: User
    ) -> BoardResponse:
        """
        Create a new board
        
        Args:
            db: Database session
            board_data: Board creation data
            owner: User creating the board
            
        Returns:
            Created board
        """
        try:
            # Check project permissions if board is being created in a project
            if board_data.project_id:
                # Check if user has permission to create boards in this project
                project_member = await db.execute(
                    select(ProjectMember).where(
                        and_(
                            ProjectMember.project_id == board_data.project_id,
                            ProjectMember.user_id == owner.id
                        )
                    )
                )
                member = project_member.scalar_one_or_none()
                if not member or not member.has_permission("create_boards"):
                    raise PermissionError("Insufficient permissions to create boards in this project")

            # Create board
            board = await DatabaseUtils.create(
                db, Board,
                name=board_data.name,
                description=board_data.description,
                owner_id=owner.id,
                project_id=board_data.project_id,
                is_public=board_data.is_public
            )
            
            # Add owner as admin member
            await DatabaseUtils.create(
                db, BoardMember,
                board_id=board.id,
                user_id=owner.id,
                role="owner",
                can_edit=True,
                can_delete=True,
                can_invite=True
            )
            
            logger.info(f"Board created: {board.id} by user {owner.uid}")
            return BoardResponse.model_validate(board)
            
        except Exception as e:
            logger.error(f"Error creating board: {e}")
            raise
    
    @staticmethod
    async def get_board(
        db: AsyncSession,
        board_id: int,
        user: User,
        include_details: bool = False
    ) -> Optional[BoardResponse]:
        """
        Get board by ID
        
        Args:
            db: Database session
            board_id: Board ID
            user: Current user
            include_details: Whether to include owner/member details
            
        Returns:
            Board if found and user has access, None otherwise
        """
        try:
            # Check if user has access to board
            has_access = await BoardService._check_board_access(db, board_id, user)
            if not has_access:
                return None
            
            if include_details:
                result = await db.execute(
                    select(Board)
                    .options(
                        selectinload(Board.owner),
                        selectinload(Board.members).selectinload(BoardMember.user)
                    )
                    .where(Board.id == board_id)
                )
                board = result.scalar_one_or_none()
                if board:
                    # Get task count
                    task_count_result = await db.execute(
                        select(func.count(Task.id)).where(Task.board_id == board_id)
                    )
                    task_count = task_count_result.scalar()
                    
                    board_dict = BoardWithDetails.model_validate(board).model_dump()
                    board_dict["task_count"] = task_count
                    return BoardWithDetails(**board_dict)
            else:
                board = await DatabaseUtils.get_by_id(db, Board, board_id)
                if board:
                    return BoardResponse.model_validate(board)
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting board {board_id}: {e}")
            return None
    
    @staticmethod
    async def update_board(
        db: AsyncSession,
        board_id: int,
        board_data: BoardUpdate,
        user: User
    ) -> Optional[BoardResponse]:
        """
        Update board
        
        Args:
            db: Database session
            board_id: Board ID
            board_data: Board update data
            user: User performing the update
            
        Returns:
            Updated board if successful, None otherwise
        """
        try:
            # Check if user can edit board
            can_edit = await BoardService._check_board_permission(db, board_id, user, "edit")
            if not can_edit:
                raise PermissionError("Insufficient permissions to update board")
            
            board = await DatabaseUtils.get_by_id(db, Board, board_id)
            if not board:
                return None
            
            # Update board fields
            update_data = {}
            for field, value in board_data.dict(exclude_unset=True).items():
                if value is not None:
                    update_data[field] = value
            
            updated_board = await DatabaseUtils.update(db, board, **update_data)
            logger.info(f"Board updated: {board_id} by user {user.uid}")
            return BoardResponse.model_validate(updated_board)
            
        except PermissionError as e:
            logger.warning(f"Board update permission error: {e}")
            raise
        except Exception as e:
            logger.error(f"Error updating board {board_id}: {e}")
            return None
    
    @staticmethod
    async def delete_board(
        db: AsyncSession,
        board_id: int,
        user: User
    ) -> bool:
        """
        Delete board
        
        Args:
            db: Database session
            board_id: Board ID
            user: User performing the deletion
            
        Returns:
            True if deletion successful, False otherwise
        """
        try:
            board = await DatabaseUtils.get_by_id(db, Board, board_id)
            if not board:
                return False
            
            # Only owner can delete board
            if board.owner_id != user.id:
                raise PermissionError("Only board owner can delete board")
            
            success = await DatabaseUtils.delete(db, board)
            if success:
                logger.info(f"Board deleted: {board_id} by user {user.uid}")
            return success
            
        except PermissionError as e:
            logger.warning(f"Board deletion permission error: {e}")
            raise
        except Exception as e:
            logger.error(f"Error deleting board {board_id}: {e}")
            return False
    
    @staticmethod
    async def list_user_boards(
        db: AsyncSession,
        user: User,
        skip: int = 0,
        limit: int = 100,
        include_details: bool = False
    ) -> List[BoardResponse]:
        """
        List boards accessible to user
        
        Args:
            db: Database session
            user: Current user
            skip: Number of records to skip
            limit: Maximum number of records to return
            include_details: Whether to include owner/member details
            
        Returns:
            List of boards
        """
        try:
            # Get boards where user is owner or member
            query = select(Board).join(BoardMember).where(
                and_(
                    BoardMember.user_id == user.id,
                    Board.is_archived == False
                )
            )
            
            if include_details:
                query = query.options(
                    selectinload(Board.owner),
                    selectinload(Board.members).selectinload(BoardMember.user)
                )
            
            query = query.offset(skip).limit(limit).order_by(Board.created_at.desc())
            
            result = await db.execute(query)
            boards = result.scalars().all()
            
            if include_details:
                board_list = []
                for board in boards:
                    # Get task count for each board
                    task_count_result = await db.execute(
                        select(func.count(Task.id)).where(Task.board_id == board.id)
                    )
                    task_count = task_count_result.scalar()
                    
                    board_dict = BoardWithDetails.model_validate(board).model_dump()
                    board_dict["task_count"] = task_count
                    board_list.append(BoardWithDetails(**board_dict))
                return board_list
            else:
                return [BoardResponse.model_validate(board) for board in boards]
            
        except Exception as e:
            logger.error(f"Error listing user boards: {e}")
            return []
    
    @staticmethod
    async def add_board_member(
        db: AsyncSession,
        board_id: int,
        member_data: BoardMemberCreate,
        inviter: User
    ) -> Optional[BoardMemberResponse]:
        """
        Add member to board
        
        Args:
            db: Database session
            board_id: Board ID
            member_data: Member data
            inviter: User adding the member
            
        Returns:
            Board member if successful, None otherwise
        """
        try:
            # Check if inviter can invite to board
            can_invite = await BoardService._check_board_permission(db, board_id, inviter, "invite")
            if not can_invite:
                raise PermissionError("Insufficient permissions to invite members")
            
            # Check if user exists
            user = await DatabaseUtils.get_by_id(db, User, member_data.user_id)
            if not user or not user.is_active:
                raise ValueError("Invalid user")
            
            # Check if user is already a member
            existing_member = await db.execute(
                select(BoardMember).where(
                    and_(
                        BoardMember.board_id == board_id,
                        BoardMember.user_id == member_data.user_id
                    )
                )
            )
            if existing_member.scalar_one_or_none():
                raise ValueError("User is already a member of this board")
            
            # Add member
            member = await DatabaseUtils.create(
                db, BoardMember,
                board_id=board_id,
                user_id=member_data.user_id,
                role=member_data.role,
                can_edit=member_data.can_edit,
                can_delete=member_data.can_delete,
                can_invite=member_data.can_invite
            )
            
            logger.info(f"Member added to board {board_id}: user {user.uid} by {inviter.uid}")
            return BoardMemberResponse.model_validate(member)
            
        except (ValueError, PermissionError) as e:
            logger.warning(f"Board member addition error: {e}")
            raise
        except Exception as e:
            logger.error(f"Error adding board member: {e}")
            return None
    
    @staticmethod
    async def _check_board_access(db: AsyncSession, board_id: int, user: User) -> bool:
        """Check if user has access to board"""
        try:
            # Check if board is public
            board = await DatabaseUtils.get_by_id(db, Board, board_id)
            if not board:
                return False
            
            if board.is_public:
                return True
            
            # Check if user is owner or member
            result = await db.execute(
                select(BoardMember).where(
                    and_(
                        BoardMember.board_id == board_id,
                        BoardMember.user_id == user.id
                    )
                )
            )
            return result.scalar_one_or_none() is not None
            
        except Exception:
            return False
    
    @staticmethod
    async def _check_board_permission(
        db: AsyncSession, 
        board_id: int, 
        user: User, 
        permission: str
    ) -> bool:
        """Check if user has specific permission on board"""
        try:
            result = await db.execute(
                select(BoardMember).where(
                    and_(
                        BoardMember.board_id == board_id,
                        BoardMember.user_id == user.id
                    )
                )
            )
            member = result.scalar_one_or_none()
            
            if not member:
                return False
            
            # Owner has all permissions
            if member.role == "owner":
                return True
            
            # Check specific permissions
            if permission == "edit":
                return member.can_edit
            elif permission == "delete":
                return member.can_delete
            elif permission == "invite":
                return member.can_invite
            
            return False
            
        except Exception:
            return False


# Global board service instance
board_service = BoardService()
