"""
Project collaboration service
"""

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, func, or_
from sqlalchemy.orm import selectinload
from typing import List, Optional, Dict, Any
import logging

from app.models.project import Project, ProjectMember, ProjectRole
from app.models.user import User
from app.models.board import Board
from app.models.task import Task
from app.schemas.project import (
    ProjectCreate, ProjectUpdate, ProjectResponse, ProjectWithDetails,
    ProjectMemberCreate, ProjectMemberUpdate, ProjectMemberResponse,
    ProjectMemberWithUser, ProjectInviteRequest, UserSearchResponse
)
from app.schemas.user import UserProfile
from app.database.utils import DatabaseUtils

logger = logging.getLogger(__name__)


class ProjectService:
    """Service for project collaboration operations"""
    
    @staticmethod
    async def create_project(
        db: AsyncSession,
        project_data: ProjectCreate,
        owner: User
    ) -> ProjectResponse:
        """
        Create a new project
        
        Args:
            db: Database session
            project_data: Project creation data
            owner: User creating the project
            
        Returns:
            Created project
        """
        try:
            # Create project
            project = await DatabaseUtils.create(
                db, Project,
                name=project_data.name,
                description=project_data.description,
                owner_id=owner.id,
                is_public=project_data.is_public
            )
            
            # Add owner as project owner member
            await DatabaseUtils.create(
                db, ProjectMember,
                project_id=project.id,
                user_id=owner.id,
                role=ProjectRole.OWNER,
                can_create_boards=True,
                can_edit_project=True,
                can_delete_project=True,
                can_manage_members=True,
                can_view_all_boards=True,
                can_create_tasks=True,
                can_assign_tasks=True
            )
            
            logger.info(f"Project created: {project.id} by user {owner.uid}")
            return ProjectResponse.model_validate(project)
            
        except Exception as e:
            logger.error(f"Error creating project: {e}")
            raise
    
    @staticmethod
    async def get_project(
        db: AsyncSession,
        project_id: int,
        user: User,
        include_details: bool = False
    ) -> Optional[ProjectResponse]:
        """
        Get project by ID
        
        Args:
            db: Database session
            project_id: Project ID
            user: Current user
            include_details: Whether to include member details
            
        Returns:
            Project if found and accessible, None otherwise
        """
        try:
            # Check access first
            if not await ProjectService._check_project_access(db, project_id, user):
                return None
            
            if include_details:
                result = await db.execute(
                    select(Project)
                    .options(
                        selectinload(Project.owner),
                        selectinload(Project.members).selectinload(ProjectMember.user)
                    )
                    .where(Project.id == project_id)
                )
                project = result.scalar_one_or_none()
                if project:
                    # Get board and task counts
                    board_count_result = await db.execute(
                        select(func.count(Board.id)).where(Board.project_id == project_id)
                    )
                    board_count = board_count_result.scalar()
                    
                    task_count_result = await db.execute(
                        select(func.count(Task.id))
                        .join(Board, Task.board_id == Board.id)
                        .where(Board.project_id == project_id)
                    )
                    task_count = task_count_result.scalar()
                    
                    project_dict = ProjectWithDetails.model_validate(project).model_dump()
                    project_dict["board_count"] = board_count
                    project_dict["task_count"] = task_count
                    return ProjectWithDetails(**project_dict)
            else:
                project = await DatabaseUtils.get_by_id(db, Project, project_id)
                if project:
                    return ProjectResponse.model_validate(project)
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting project {project_id}: {e}")
            return None
    
    @staticmethod
    async def update_project(
        db: AsyncSession,
        project_id: int,
        project_data: ProjectUpdate,
        user: User
    ) -> Optional[ProjectResponse]:
        """
        Update project
        
        Args:
            db: Database session
            project_id: Project ID
            project_data: Update data
            user: Current user
            
        Returns:
            Updated project if successful, None otherwise
        """
        try:
            # Check permission
            if not await ProjectService._check_project_permission(db, project_id, user, "edit_project"):
                raise PermissionError("Insufficient permissions to edit project")
            
            project = await DatabaseUtils.get_by_id(db, Project, project_id)
            if not project:
                return None
            
            # Prepare update data
            update_data = {}
            for field, value in project_data.model_dump(exclude_unset=True).items():
                if value is not None:
                    update_data[field] = value
            
            updated_project = await DatabaseUtils.update(db, project, **update_data)
            logger.info(f"Project updated: {project_id} by user {user.uid}")
            return ProjectResponse.model_validate(updated_project)
            
        except PermissionError as e:
            logger.warning(f"Project update permission error: {e}")
            raise
        except Exception as e:
            logger.error(f"Error updating project {project_id}: {e}")
            raise
    
    @staticmethod
    async def delete_project(
        db: AsyncSession,
        project_id: int,
        user: User
    ) -> bool:
        """
        Delete project
        
        Args:
            db: Database session
            project_id: Project ID
            user: Current user
            
        Returns:
            True if deleted successfully, False otherwise
        """
        try:
            # Check permission
            if not await ProjectService._check_project_permission(db, project_id, user, "delete_project"):
                raise PermissionError("Insufficient permissions to delete project")
            
            project = await DatabaseUtils.get_by_id(db, Project, project_id)
            if not project:
                return False
            
            # Archive instead of hard delete
            await DatabaseUtils.update(db, project, is_archived=True)
            logger.info(f"Project archived: {project_id} by user {user.uid}")
            return True
            
        except PermissionError as e:
            logger.warning(f"Project deletion permission error: {e}")
            raise
        except Exception as e:
            logger.error(f"Error deleting project {project_id}: {e}")
            return False

    @staticmethod
    async def list_user_projects(
        db: AsyncSession,
        user: User,
        skip: int = 0,
        limit: int = 100,
        include_details: bool = False
    ) -> List[ProjectResponse]:
        """
        List projects accessible to user

        Args:
            db: Database session
            user: Current user
            skip: Number of records to skip
            limit: Maximum number of records to return
            include_details: Whether to include member details

        Returns:
            List of accessible projects
        """
        try:
            # Get projects where user is a member or owner
            query = (
                select(Project)
                .join(ProjectMember, Project.id == ProjectMember.project_id)
                .where(
                    and_(
                        ProjectMember.user_id == user.id,
                        Project.is_archived == False
                    )
                )
                .offset(skip)
                .limit(limit)
                .order_by(Project.created_at.desc())
            )

            if include_details:
                query = query.options(
                    selectinload(Project.owner),
                    selectinload(Project.members).selectinload(ProjectMember.user)
                )

                result = await db.execute(query)
                projects = result.scalars().all()

                project_list = []
                for project in projects:
                    # Get board and task counts
                    board_count_result = await db.execute(
                        select(func.count(Board.id)).where(Board.project_id == project.id)
                    )
                    board_count = board_count_result.scalar()

                    task_count_result = await db.execute(
                        select(func.count(Task.id))
                        .join(Board, Task.board_id == Board.id)
                        .where(Board.project_id == project.id)
                    )
                    task_count = task_count_result.scalar()

                    project_dict = ProjectWithDetails.model_validate(project).model_dump()
                    project_dict["board_count"] = board_count
                    project_dict["task_count"] = task_count
                    project_list.append(ProjectWithDetails(**project_dict))
                return project_list
            else:
                result = await db.execute(query)
                projects = result.scalars().all()
                return [ProjectResponse.model_validate(project) for project in projects]

        except Exception as e:
            logger.error(f"Error listing user projects: {e}")
            return []

    @staticmethod
    async def add_project_member(
        db: AsyncSession,
        project_id: int,
        member_data: ProjectMemberCreate,
        inviter: User
    ) -> Optional[ProjectMemberResponse]:
        """
        Add member to project

        Args:
            db: Database session
            project_id: Project ID
            member_data: Member data
            inviter: User adding the member

        Returns:
            Created member if successful, None otherwise
        """
        try:
            # Check permission
            if not await ProjectService._check_project_permission(db, project_id, inviter, "manage_members"):
                raise PermissionError("Insufficient permissions to manage members")

            # Check if user exists
            user = await DatabaseUtils.get_by_id(db, User, member_data.user_id)
            if not user or not user.is_active:
                raise ValueError("Invalid user")

            # Check if user is already a member
            existing_member = await db.execute(
                select(ProjectMember).where(
                    and_(
                        ProjectMember.project_id == project_id,
                        ProjectMember.user_id == member_data.user_id
                    )
                )
            )
            if existing_member.scalar_one_or_none():
                raise ValueError("User is already a member")

            # Create member
            member = await DatabaseUtils.create(
                db, ProjectMember,
                project_id=project_id,
                user_id=member_data.user_id,
                role=member_data.role,
                can_create_boards=member_data.can_create_boards,
                can_edit_project=member_data.can_edit_project,
                can_delete_project=member_data.can_delete_project,
                can_manage_members=member_data.can_manage_members,
                can_view_all_boards=member_data.can_view_all_boards,
                can_create_tasks=member_data.can_create_tasks,
                can_assign_tasks=member_data.can_assign_tasks
            )

            logger.info(f"Member added to project {project_id}: user {user.uid} by {inviter.uid}")
            return ProjectMemberResponse.model_validate(member)

        except (ValueError, PermissionError) as e:
            logger.warning(f"Project member addition error: {e}")
            raise
        except Exception as e:
            logger.error(f"Error adding project member: {e}")
            raise

    @staticmethod
    async def search_users(
        db: AsyncSession,
        query: str,
        current_user: User,
        limit: int = 10
    ) -> UserSearchResponse:
        """
        Search users by username for adding to project

        Args:
            db: Database session
            query: Search query
            current_user: Current user
            limit: Maximum results

        Returns:
            Search results
        """
        try:
            # Search users by username
            search_query = (
                select(User)
                .where(
                    and_(
                        User.username.ilike(f"%{query}%"),
                        User.is_active == True,
                        User.id != current_user.id  # Exclude current user
                    )
                )
                .limit(limit)
            )

            result = await db.execute(search_query)
            users = result.scalars().all()

            user_profiles = [UserProfile.model_validate(user).model_dump() for user in users]

            return UserSearchResponse(
                users=user_profiles,
                total=len(user_profiles)
            )

        except Exception as e:
            logger.error(f"Error searching users: {e}")
            return UserSearchResponse(users=[], total=0)

    @staticmethod
    async def _check_project_access(db: AsyncSession, project_id: int, user: User) -> bool:
        """Check if user has access to project"""
        try:
            # Check if project is public
            project = await DatabaseUtils.get_by_id(db, Project, project_id)
            if not project or project.is_archived:
                return False

            if project.is_public:
                return True

            # Check if user is owner or member
            result = await db.execute(
                select(ProjectMember).where(
                    and_(
                        ProjectMember.project_id == project_id,
                        ProjectMember.user_id == user.id
                    )
                )
            )
            return result.scalar_one_or_none() is not None

        except Exception:
            return False

    @staticmethod
    async def _check_project_permission(
        db: AsyncSession,
        project_id: int,
        user: User,
        permission: str
    ) -> bool:
        """Check if user has specific permission on project"""
        try:
            result = await db.execute(
                select(ProjectMember).where(
                    and_(
                        ProjectMember.project_id == project_id,
                        ProjectMember.user_id == user.id
                    )
                )
            )
            member = result.scalar_one_or_none()

            if not member:
                return False

            return member.has_permission(permission)

        except Exception:
            return False


# Global project service instance
project_service = ProjectService()
