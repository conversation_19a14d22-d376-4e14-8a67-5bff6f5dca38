"""
Authentication service for Pi Network integration
"""

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from typing import Optional, Dict, Any
from datetime import datetime, timedelta
from jose import JWTError, jwt
import logging

from app.models.user import User
from app.schemas.user import User<PERSON><PERSON>, UserResponse
from app.core.config import settings
from app.services.pi_client import pi_client
from app.database.utils import DatabaseUtils

logger = logging.getLogger(__name__)


class AuthService:
    """Authentication service for Pi Network users"""
    
    @staticmethod
    def create_access_token(data: Dict[str, Any], expires_delta: Optional[timedelta] = None) -> str:
        """
        Create JWT access token
        
        Args:
            data: Data to encode in token
            expires_delta: Token expiration time
            
        Returns:
            JWT token string
        """
        to_encode = data.copy()
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
        
        to_encode.update({"exp": expire})
        encoded_jwt = jwt.encode(to_encode, settings.SECRET_KEY, algorithm=settings.ALGORITHM)
        return encoded_jwt
    
    @staticmethod
    def verify_token(token: str) -> Optional[Dict[str, Any]]:
        """
        Verify JWT token
        
        Args:
            token: JWT token string
            
        Returns:
            Token payload if valid, None otherwise
        """
        try:
            payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM])
            return payload
        except JWTError:
            return None
    
    @staticmethod
    async def authenticate_pi_user(
        db: AsyncSession, 
        access_token: str, 
        user_data: Dict[str, Any]
    ) -> Optional[UserResponse]:
        """
        Authenticate Pi Network user
        
        Args:
            db: Database session
            access_token: Pi Network access token
            user_data: User data from Pi Network
            
        Returns:
            User response if authentication successful, None otherwise
        """
        try:
            # Verify access token with Pi Platform
            verified_user = await pi_client.verify_user(access_token)
            if not verified_user:
                logger.warning("Pi access token verification failed")
                return None
            
            # Check if user exists
            existing_user = await DatabaseUtils.get_by_field(
                db, User, "uid", user_data["uid"]
            )
            
            if existing_user:
                # Update existing user's access token
                updated_user = await DatabaseUtils.update(
                    db, existing_user,
                    access_token=access_token,
                    username=user_data.get("username", existing_user.username),
                    roles=user_data.get("roles", existing_user.roles)
                )
                logger.info(f"Updated existing user: {updated_user.uid}")
                return UserResponse.model_validate(updated_user)
            else:
                # Create new user
                new_user = await DatabaseUtils.create(
                    db, User,
                    uid=user_data["uid"],
                    username=user_data["username"],
                    roles=user_data.get("roles", []),
                    access_token=access_token,
                    is_active=True,
                    is_verified=True
                )
                logger.info(f"Created new user: {new_user.uid}")
                return UserResponse.model_validate(new_user)
                
        except Exception as e:
            logger.error(f"Error authenticating Pi user: {e}")
            return None
    
    @staticmethod
    async def get_current_user(db: AsyncSession, token: str) -> Optional[User]:
        """
        Get current user from JWT token
        
        Args:
            db: Database session
            token: JWT token
            
        Returns:
            User if token is valid, None otherwise
        """
        try:
            payload = AuthService.verify_token(token)
            if not payload:
                return None
            
            user_uid: str = payload.get("sub")
            if not user_uid:
                return None
            
            user = await DatabaseUtils.get_by_field(db, User, "uid", user_uid)
            if not user or not user.is_active:
                return None
            
            return user
            
        except Exception as e:
            logger.error(f"Error getting current user: {e}")
            return None
    
    @staticmethod
    async def refresh_pi_token(db: AsyncSession, user: User, new_access_token: str) -> bool:
        """
        Refresh Pi Network access token for user
        
        Args:
            db: Database session
            user: User instance
            new_access_token: New Pi access token
            
        Returns:
            True if refresh successful, False otherwise
        """
        try:
            # Verify new token with Pi Platform
            verified_user = await pi_client.verify_user(new_access_token)
            if not verified_user or verified_user.get("uid") != user.uid:
                logger.warning("Pi token refresh verification failed")
                return False
            
            # Update user's access token
            await DatabaseUtils.update(db, user, access_token=new_access_token)
            logger.info(f"Refreshed Pi token for user: {user.uid}")
            return True
            
        except Exception as e:
            logger.error(f"Error refreshing Pi token: {e}")
            return False
    
    @staticmethod
    async def deactivate_user(db: AsyncSession, user: User) -> bool:
        """
        Deactivate user account
        
        Args:
            db: Database session
            user: User instance
            
        Returns:
            True if deactivation successful, False otherwise
        """
        try:
            await DatabaseUtils.update(
                db, user, 
                is_active=False, 
                access_token=None
            )
            logger.info(f"Deactivated user: {user.uid}")
            return True
            
        except Exception as e:
            logger.error(f"Error deactivating user: {e}")
            return False


# Global auth service instance
auth_service = AuthService()
