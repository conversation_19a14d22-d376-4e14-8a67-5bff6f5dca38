"""
Project model for team collaboration and organization
"""

from sqlmodel import Field, Relationship
from enum import Enum
from typing import List, Optional
from .base import BaseModel


class ProjectRole(str, Enum):
    """Project member roles"""
    OWNER = "owner"
    ADMIN = "admin"
    MEMBER = "member"
    VIEWER = "viewer"


class Project(BaseModel, table=True):
    """Project model for organizing boards and teams"""
    __tablename__ = "projects"

    # Basic project information
    name: str = Field(max_length=255)
    description: Optional[str] = Field(default=None)

    # Project ownership
    owner_id: int = Field(foreign_key="users.id")

    # Project settings
    is_public: bool = Field(default=False)
    is_archived: bool = Field(default=False)

    # Relationships
    owner: Optional["User"] = Relationship(sa_relationship_kwargs={"foreign_keys": "Project.owner_id"})
    boards: List["Board"] = Relationship(back_populates="project")
    members: List["ProjectMember"] = Relationship(back_populates="project")

    def __repr__(self):
        return f"<Project(id={self.id}, name='{self.name}')>"


class ProjectMember(BaseModel, table=True):
    """Project membership model with role-based permissions"""
    __tablename__ = "project_members"

    # Foreign keys
    project_id: int = Field(foreign_key="projects.id")
    user_id: int = Field(foreign_key="users.id")

    # Member role and permissions
    role: ProjectRole = Field(default=ProjectRole.MEMBER)

    # Granular permissions
    can_create_boards: bool = Field(default=True)
    can_edit_project: bool = Field(default=False)
    can_delete_project: bool = Field(default=False)
    can_manage_members: bool = Field(default=False)
    can_view_all_boards: bool = Field(default=True)
    can_create_tasks: bool = Field(default=True)
    can_assign_tasks: bool = Field(default=True)

    # Relationships
    project: Optional["Project"] = Relationship(back_populates="members")
    user: Optional["User"] = Relationship(back_populates="project_memberships")
    
    def __repr__(self):
        return f"<ProjectMember(project_id={self.project_id}, user_id={self.user_id}, role='{self.role}')>"
    
    def has_permission(self, permission: str) -> bool:
        """Check if member has specific permission"""
        # Owner has all permissions
        if self.role == ProjectRole.OWNER:
            return True
        
        # Admin has most permissions except project deletion
        if self.role == ProjectRole.ADMIN:
            if permission == "delete_project":
                return False
            return True
        
        # Check specific permissions based on role and settings
        permission_map = {
            "create_boards": self.can_create_boards,
            "edit_project": self.can_edit_project,
            "delete_project": self.can_delete_project,
            "manage_members": self.can_manage_members,
            "view_all_boards": self.can_view_all_boards,
            "create_tasks": self.can_create_tasks,
            "assign_tasks": self.can_assign_tasks,
        }
        
        return permission_map.get(permission, False)
