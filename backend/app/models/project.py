"""
Project model for team collaboration and organization
"""

from sqlmodel import Field, Relationship
from enum import Enum
from typing import List, Optional
from datetime import datetime
from .base import BaseModel


class ProjectRole(str, Enum):
    """Project member roles"""
    OWNER = "owner"
    ADMIN = "admin"
    MEMBER = "member"
    VIEWER = "viewer"


class Project(BaseModel, table=True):
    """Project model for organizing boards and teams"""
    __tablename__ = "projects"

    # Basic project information
    name: str = Field(max_length=255)
    description: Optional[str] = Field(default=None)

    # Project ownership - using created_by for authorization
    created_by: int = Field(foreign_key="users.id")  # Project Owner

    # Project settings
    is_public: bool = Field(default=False)
    is_archived: bool = Field(default=False)

    # Relationships
    owner: Optional["User"] = Relationship(sa_relationship_kwargs={"foreign_keys": "Project.created_by"})
    boards: List["Board"] = Relationship(back_populates="project")
    members: List["ProjectMember"] = Relationship(back_populates="project")

    def __repr__(self):
        return f"<Project(id={self.id}, name='{self.name}')>"


class ProjectMember(BaseModel, table=True):
    """Project membership model with ownership-based permissions"""
    __tablename__ = "project_members"

    # Foreign keys
    project_id: int = Field(foreign_key="projects.id")
    user_id: int = Field(foreign_key="users.id")

    # Manager status - only project owners can promote members to managers
    is_manager: bool = Field(default=False)  # Project Manager role

    # Join date for tracking
    joined_at: Optional[datetime] = Field(default=None)

    # Relationships
    project: Optional["Project"] = Relationship(back_populates="members")
    user: Optional["User"] = Relationship(back_populates="project_memberships")
    
    def __repr__(self):
        return f"<ProjectMember(project_id={self.project_id}, user_id={self.user_id}, is_manager={self.is_manager})>"

    def get_user_type(self, user_id: int) -> str:
        """Get user type for authorization"""
        # Check if user is project owner
        if self.project and self.project.created_by == user_id:
            return "project_owner"

        # Check if user is project manager
        if self.user_id == user_id and self.is_manager:
            return "project_manager"

        # Regular member
        if self.user_id == user_id:
            return "member"

        return "viewer"
