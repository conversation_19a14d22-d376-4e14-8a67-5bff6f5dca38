"""
Board model for team collaboration
"""

from sqlmodel import Field, Relationship
from typing import List, Optional
from .base import BaseModel


class Board(BaseModel, table=True):
    """Board model for team collaboration"""
    __tablename__ = "boards"

    # Basic board information
    name: str = Field(max_length=255)
    description: Optional[str] = Field(default=None)

    # Board ownership and project association
    owner_id: int = Field(foreign_key="users.id")
    project_id: Optional[int] = Field(default=None, foreign_key="projects.id")

    # Board settings
    is_public: bool = Field(default=False)
    is_archived: bool = Field(default=False)

    # Relationships
    owner: Optional["User"] = Relationship(sa_relationship_kwargs={"foreign_keys": "Board.owner_id"})
    project: Optional["Project"] = Relationship(back_populates="boards")
    tasks: List["Task"] = Relationship(back_populates="board")
    members: List["BoardMember"] = Relationship(back_populates="board")

    def __repr__(self):
        return f"<Board(id={self.id}, name='{self.name}')>"


class BoardMember(BaseModel, table=True):
    """Board membership model (many-to-many relationship)"""
    __tablename__ = "board_members"

    # Foreign keys
    board_id: int = Field(foreign_key="boards.id")
    user_id: int = Field(foreign_key="users.id")

    # Member role and permissions
    role: str = Field(default="member", max_length=50)  # owner, admin, member, viewer
    can_edit: bool = Field(default=True)
    can_delete: bool = Field(default=False)
    can_invite: bool = Field(default=False)

    # Relationships
    board: Optional["Board"] = Relationship(back_populates="members")
    user: Optional["User"] = Relationship(back_populates="board_memberships")

    def __repr__(self):
        return f"<BoardMember(board_id={self.board_id}, user_id={self.user_id}, role='{self.role}')>"
