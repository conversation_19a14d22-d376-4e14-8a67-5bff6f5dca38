"""
Board model for team collaboration
"""

from sqlmodel import Field, Relationship
from typing import List, Optional
from datetime import datetime
from .base import BaseModel


class Board(BaseModel, table=True):
    """Board model for team collaboration"""
    __tablename__ = "boards"

    # Basic board information
    name: str = Field(max_length=255)
    description: Optional[str] = Field(default=None)

    # Board ownership and project association - using created_by for authorization
    created_by: int = Field(foreign_key="users.id")  # Board Owner
    project_id: Optional[int] = Field(default=None, foreign_key="projects.id")

    # Board settings
    is_public: bool = Field(default=False)
    is_archived: bool = Field(default=False)

    # Relationships
    owner: Optional["User"] = Relationship(sa_relationship_kwargs={"foreign_keys": "Board.created_by"})
    project: Optional["Project"] = Relationship(back_populates="boards")
    tasks: List["Task"] = Relationship(back_populates="board")
    members: List["BoardMember"] = Relationship(back_populates="board")

    def __repr__(self):
        return f"<Board(id={self.id}, name='{self.name}')>"


class BoardMember(BaseModel, table=True):
    """Board membership model - simplified for ownership-based authorization"""
    __tablename__ = "board_members"

    # Foreign keys
    board_id: int = Field(foreign_key="boards.id")
    user_id: int = Field(foreign_key="users.id")

    # Simple membership tracking - permissions determined by ownership hierarchy
    joined_at: Optional[datetime] = Field(default=None)

    # Relationships
    board: Optional["Board"] = Relationship(back_populates="members")
    user: Optional["User"] = Relationship(back_populates="board_memberships")

    def __repr__(self):
        return f"<BoardMember(board_id={self.board_id}, user_id={self.user_id})>"

    def get_user_type(self, user_id: int) -> str:
        """Get user type for authorization"""
        # Check if user is board owner
        if self.board and self.board.created_by == user_id:
            return "board_owner"

        # Check if user is project owner (if board belongs to project)
        if self.board and self.board.project and self.board.project.created_by == user_id:
            return "project_owner"

        # Regular member
        if self.user_id == user_id:
            return "member"

        return "viewer"
