"""
Support ticket model
"""

from sqlmodel import Field, Relationship
from datetime import datetime
import enum
import uuid
from typing import Optional

from .base import BaseModel


class TicketStatus(str, enum.Enum):
    OPEN = "OPEN"
    IN_PROGRESS = "IN_PROGRESS"
    RESOLVED = "RESOLVED"
    CLOSED = "CLOSED"


class TicketPriority(str, enum.Enum):
    LOW = "LOW"
    MEDIUM = "MEDIUM"
    HIGH = "HIGH"


class TicketCategory(str, enum.Enum):
    GENERAL = "general"
    TECHNICAL = "technical"
    BILLING = "billing"
    FEATURE_REQUEST = "feature_request"
    BUG_REPORT = "bug_report"


class SupportTicket(BaseModel, table=True):
    """Support ticket model"""

    __tablename__ = "support_tickets"

    id: str = Field(default_factory=lambda: str(uuid.uuid4()), primary_key=True)
    user_id: int = Field(foreign_key="users.id")
    ticket_number: str = Field(unique=True, max_length=50)
    subject: str = Field(max_length=255)
    message: str
    category: TicketCategory = Field(default=TicketCategory.GENERAL)
    status: TicketStatus = Field(default=TicketStatus.OPEN)
    priority: TicketPriority = Field(default=TicketPriority.MEDIUM)

    # Relationships
    user: Optional["User"] = Relationship(back_populates="support_tickets")

    def __repr__(self):
        return f"<SupportTicket {self.ticket_number}: {self.subject}>"
