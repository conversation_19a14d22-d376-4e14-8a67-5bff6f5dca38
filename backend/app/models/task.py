"""
Task model for project management
"""

from sqlmodel import Field, Relationship
from enum import Enum as PyEnum
from typing import Optional
from datetime import datetime
from .base import BaseModel


class TaskStatus(str, PyEnum):
    """Task status enumeration"""
    TODO = "todo"
    IN_PROGRESS = "in_progress"
    IN_REVIEW = "in_review"
    COMPLETED = "completed"
    CANCELLED = "cancelled"


class TaskPriority(str, PyEnum):
    """Task priority enumeration"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    URGENT = "urgent"


class Task(BaseModel, table=True):
    """Task model for project management"""
    __tablename__ = "tasks"

    # Basic task information
    title: str = Field(max_length=255)
    description: Optional[str] = Field(default=None)

    # Task assignment and ownership - using created_by for authorization
    created_by: int = Field(foreign_key="users.id")  # Task creator
    assignee_id: Optional[int] = Field(default=None, foreign_key="users.id")

    # Task properties
    status: TaskStatus = Field(default=TaskStatus.TODO)
    priority: TaskPriority = Field(default=TaskPriority.MEDIUM)

    # Dates
    due_date: Optional[datetime] = Field(default=None)
    completed_at: Optional[datetime] = Field(default=None)

    # Board association
    board_id: Optional[int] = Field(default=None, foreign_key="boards.id")

    # Relationships
    creator: Optional["User"] = Relationship(back_populates="created_tasks", sa_relationship_kwargs={"foreign_keys": "Task.created_by"})
    assignee: Optional["User"] = Relationship(back_populates="tasks", sa_relationship_kwargs={"foreign_keys": "Task.assignee_id"})
    board: Optional["Board"] = Relationship(back_populates="tasks")

    def __repr__(self):
        return f"<Task(id={self.id}, title='{self.title}', status='{self.status.value}')>"

    def get_user_type(self, user_id: int) -> str:
        """Get user type for authorization"""
        # Check if user is task creator
        if self.created_by == user_id:
            return "task_creator"

        # Check if user is task assignee
        if self.assignee_id == user_id:
            return "assignee"

        # Check if user is board owner
        if self.board and self.board.created_by == user_id:
            return "board_owner"

        # Check if user is project owner (if task belongs to project via board)
        if self.board and self.board.project and self.board.project.created_by == user_id:
            return "project_owner"

        return "viewer"
