"""
Task model for project management
"""

from sqlmodel import Field, Relationship
from enum import Enum as PyEnum
from typing import Optional
from datetime import datetime
from .base import BaseModel


class TaskStatus(str, PyEnum):
    """Task status enumeration"""
    TODO = "todo"
    IN_PROGRESS = "in_progress"
    IN_REVIEW = "in_review"
    COMPLETED = "completed"
    CANCELLED = "cancelled"


class TaskPriority(str, PyEnum):
    """Task priority enumeration"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    URGENT = "urgent"


class Task(BaseModel, table=True):
    """Task model for project management"""
    __tablename__ = "tasks"

    # Basic task information
    title: str = Field(max_length=255)
    description: Optional[str] = Field(default=None)

    # Task assignment and ownership
    creator_id: int = Field(foreign_key="users.id")
    assignee_id: Optional[int] = Field(default=None, foreign_key="users.id")

    # Task properties
    status: TaskStatus = Field(default=TaskStatus.TODO)
    priority: TaskPriority = Field(default=TaskPriority.MEDIUM)

    # Dates
    due_date: Optional[datetime] = Field(default=None)
    completed_at: Optional[datetime] = Field(default=None)

    # Board association
    board_id: Optional[int] = Field(default=None, foreign_key="boards.id")

    # Relationships
    creator: Optional["User"] = Relationship(back_populates="created_tasks", sa_relationship_kwargs={"foreign_keys": "Task.creator_id"})
    assignee: Optional["User"] = Relationship(back_populates="tasks", sa_relationship_kwargs={"foreign_keys": "Task.assignee_id"})
    board: Optional["Board"] = Relationship(back_populates="tasks")

    def __repr__(self):
        return f"<Task(id={self.id}, title='{self.title}', status='{self.status.value}')>"
