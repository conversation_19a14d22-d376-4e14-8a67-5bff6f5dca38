"""
User model for Pi Network authentication
"""

from sqlmodel import Field, Relationship, Column, JSON
from typing import List, Optional
from .base import BaseModel


class User(BaseModel, table=True):
    """User model based on Pi Network authentication"""
    __tablename__ = "users"

    # Pi Network specific fields
    uid: str = Field(unique=True, index=True, max_length=255)  # Pi Network user ID
    username: str = Field(max_length=255)  # Pi Network username
    roles: List[str] = Field(default_factory=list, sa_column=Column(JSON))  # User roles array
    access_token: Optional[str] = Field(default=None)  # Current Pi access token

    # User status
    is_active: bool = Field(default=True)
    is_verified: bool = Field(default=False)

    # Relationships
    tasks: List["Task"] = Relationship(back_populates="assignee", sa_relationship_kwargs={"foreign_keys": "Task.assignee_id"})
    created_tasks: List["Task"] = Relationship(back_populates="creator", sa_relationship_kwargs={"foreign_keys": "Task.creator_id"})
    payments: List["Payment"] = Relationship(back_populates="user")
    subscriptions: List["Subscription"] = Relationship(back_populates="user")
    board_memberships: List["BoardMember"] = Relationship(back_populates="user")
    project_memberships: List["ProjectMember"] = Relationship(back_populates="user")
    support_tickets: List["SupportTicket"] = Relationship(back_populates="user")

    def __repr__(self):
        return f"<User(uid='{self.uid}', username='{self.username}')>"
