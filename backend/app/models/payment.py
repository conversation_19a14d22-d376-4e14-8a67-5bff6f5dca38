"""
Payment and subscription models for Pi Network integration
"""

from sqlmodel import Field, Relationship
from enum import Enum as PyEnum
from typing import Optional
from datetime import datetime
from decimal import Decimal
from .base import BaseModel


class PaymentStatus(str, PyEnum):
    """Payment status enumeration"""
    PENDING = "pending"
    APPROVED = "approved"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class SubscriptionStatus(str, PyEnum):
    """Subscription status enumeration"""
    ACTIVE = "active"
    EXPIRED = "expired"
    CANCELLED = "cancelled"
    PENDING = "pending"


class SubscriptionPlan(str, PyEnum):
    """Subscription plan enumeration"""
    FREE_TRIAL = "free_trial"
    BASIC = "basic"
    STANDARD = "standard"
    PREMIUM = "premium"


class Payment(BaseModel, table=True):
    """Payment model for Pi Network transactions"""
    __tablename__ = "payments"

    # Pi Network payment information
    pi_payment_id: str = Field(unique=True, index=True, max_length=255)
    txid: Optional[str] = Field(default=None, index=True, max_length=255)  # Blockchain transaction ID

    # Payment details
    user_id: int = Field(foreign_key="users.id")
    amount: Decimal = Field(decimal_places=2, max_digits=10)
    memo: Optional[str] = Field(default=None)

    # Payment status and metadata
    status: PaymentStatus = Field(default=PaymentStatus.PENDING)
    payment_metadata: Optional[str] = Field(default=None)  # JSON string for additional data

    # Timestamps
    approved_at: Optional[datetime] = Field(default=None)
    completed_at: Optional[datetime] = Field(default=None)

    # Relationships
    user: Optional["User"] = Relationship(back_populates="payments")
    subscription: Optional["Subscription"] = Relationship(back_populates="payment", sa_relationship_kwargs={"uselist": False})

    def __repr__(self):
        return f"<Payment(id={self.id}, pi_payment_id='{self.pi_payment_id}', status='{self.status.value}')>"


class Subscription(BaseModel, table=True):
    """Subscription model for user plans"""
    __tablename__ = "subscriptions"

    # User and plan information
    user_id: int = Field(foreign_key="users.id")
    plan: SubscriptionPlan

    # Subscription details
    status: SubscriptionStatus = Field(default=SubscriptionStatus.PENDING)

    # Dates
    start_date: datetime
    end_date: datetime

    # Payment association
    payment_id: Optional[int] = Field(default=None, foreign_key="payments.id")

    # Subscription features (JSON string)
    features: Optional[str] = Field(default=None)

    # Auto-renewal (disabled by default for security)
    auto_renew: bool = Field(default=False)

    # Relationships
    user: Optional["User"] = Relationship(back_populates="subscriptions")
    payment: Optional["Payment"] = Relationship(back_populates="subscription")

    def __repr__(self):
        return f"<Subscription(id={self.id}, user_id={self.user_id}, plan='{self.plan.value}', status='{self.status.value}')>"
