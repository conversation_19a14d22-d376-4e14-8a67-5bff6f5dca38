"""Add project management tables

Revision ID: 16393be1100d
Revises: 
Create Date: 2025-08-23 18:51:45.232929

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '16393be1100d'
down_revision = None
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('projects',
    sa.Column('name', sa.String(length=255), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('owner_id', sa.Integer(), nullable=False),
    sa.Column('is_public', sa.<PERSON>an(), nullable=True),
    sa.Column('is_archived', sa.<PERSON>(), nullable=True),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['owner_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_projects_id'), 'projects', ['id'], unique=False)
    op.create_table('project_members',
    sa.Column('project_id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('role', sa.Enum('OWNER', 'ADMIN', 'MEMBER', 'VIEWER', name='projectrole'), nullable=False),
    sa.Column('can_create_boards', sa.Boolean(), nullable=True),
    sa.Column('can_edit_project', sa.Boolean(), nullable=True),
    sa.Column('can_delete_project', sa.Boolean(), nullable=True),
    sa.Column('can_manage_members', sa.Boolean(), nullable=True),
    sa.Column('can_view_all_boards', sa.Boolean(), nullable=True),
    sa.Column('can_create_tasks', sa.Boolean(), nullable=True),
    sa.Column('can_assign_tasks', sa.Boolean(), nullable=True),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['project_id'], ['projects.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_project_members_id'), 'project_members', ['id'], unique=False)

    # Use batch mode for SQLite to add column and foreign key
    with op.batch_alter_table('boards', schema=None) as batch_op:
        batch_op.add_column(sa.Column('project_id', sa.Integer(), nullable=True))
        batch_op.create_foreign_key('fk_boards_project_id', 'projects', ['project_id'], ['id'])
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    # Use batch mode for SQLite to drop foreign key and column
    with op.batch_alter_table('boards', schema=None) as batch_op:
        batch_op.drop_constraint('fk_boards_project_id', type_='foreignkey')
        batch_op.drop_column('project_id')

    op.drop_index(op.f('ix_project_members_id'), table_name='project_members')
    op.drop_table('project_members')
    op.drop_index(op.f('ix_projects_id'), table_name='projects')
    op.drop_table('projects')
    # ### end Alembic commands ###
