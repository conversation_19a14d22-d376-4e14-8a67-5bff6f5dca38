"""Add support tickets table

Revision ID: acc5a74446e1
Revises: 3607d72d8eaf
Create Date: 2025-08-25 20:25:44.868144

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'acc5a74446e1'
down_revision = '3607d72d8eaf'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('support_tickets')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('support_tickets',
    sa.Column('created_at', sa.DATETIME(), nullable=False),
    sa.Column('updated_at', sa.DATETIME(), nullable=False),
    sa.Column('id', sa.VARCHAR(), nullable=False),
    sa.Column('user_id', sa.VARCHAR(), nullable=False),
    sa.Column('ticket_number', sa.VARCHAR(length=50), nullable=False),
    sa.Column('subject', sa.VARCHAR(length=255), nullable=False),
    sa.Column('message', sa.VARCHAR(), nullable=False),
    sa.Column('category', sa.VARCHAR(length=15), nullable=False),
    sa.Column('status', sa.VARCHAR(length=11), nullable=False),
    sa.Column('priority', sa.VARCHAR(length=6), nullable=False),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('ticket_number')
    )
    # ### end Alembic commands ###
