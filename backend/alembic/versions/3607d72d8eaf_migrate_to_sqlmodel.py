"""Migrate_to_SQLModel

Revision ID: 3607d72d8eaf
Revises: 16393be1100d
Create Date: 2025-08-23 19:11:44.707581

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '3607d72d8eaf'
down_revision = '16393be1100d'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('board_members', 'role',
               existing_type=sa.VARCHAR(length=50),
               nullable=False)
    op.alter_column('board_members', 'can_edit',
               existing_type=sa.BOOLEAN(),
               nullable=False)
    op.alter_column('board_members', 'can_delete',
               existing_type=sa.BOOLEAN(),
               nullable=False)
    op.alter_column('board_members', 'can_invite',
               existing_type=sa.BOOLEAN(),
               nullable=False)
    op.drop_index('ix_board_members_id', table_name='board_members')
    op.alter_column('boards', 'description',
               existing_type=sa.TEXT(),
               type_=sqlmodel.sql.sqltypes.AutoString(),
               existing_nullable=True)
    op.alter_column('boards', 'is_public',
               existing_type=sa.BOOLEAN(),
               nullable=False)
    op.alter_column('boards', 'is_archived',
               existing_type=sa.BOOLEAN(),
               nullable=False)
    op.drop_index('ix_boards_id', table_name='boards')
    op.create_foreign_key(None, 'boards', 'projects', ['project_id'], ['id'])
    op.alter_column('payments', 'memo',
               existing_type=sa.TEXT(),
               type_=sqlmodel.sql.sqltypes.AutoString(),
               existing_nullable=True)
    op.alter_column('payments', 'payment_metadata',
               existing_type=sa.TEXT(),
               type_=sqlmodel.sql.sqltypes.AutoString(),
               existing_nullable=True)
    op.drop_index('ix_payments_id', table_name='payments')
    op.alter_column('project_members', 'can_create_boards',
               existing_type=sa.BOOLEAN(),
               nullable=False)
    op.alter_column('project_members', 'can_edit_project',
               existing_type=sa.BOOLEAN(),
               nullable=False)
    op.alter_column('project_members', 'can_delete_project',
               existing_type=sa.BOOLEAN(),
               nullable=False)
    op.alter_column('project_members', 'can_manage_members',
               existing_type=sa.BOOLEAN(),
               nullable=False)
    op.alter_column('project_members', 'can_view_all_boards',
               existing_type=sa.BOOLEAN(),
               nullable=False)
    op.alter_column('project_members', 'can_create_tasks',
               existing_type=sa.BOOLEAN(),
               nullable=False)
    op.alter_column('project_members', 'can_assign_tasks',
               existing_type=sa.BOOLEAN(),
               nullable=False)
    op.drop_index('ix_project_members_id', table_name='project_members')
    op.alter_column('projects', 'description',
               existing_type=sa.TEXT(),
               type_=sqlmodel.sql.sqltypes.AutoString(),
               existing_nullable=True)
    op.alter_column('projects', 'is_public',
               existing_type=sa.BOOLEAN(),
               nullable=False)
    op.alter_column('projects', 'is_archived',
               existing_type=sa.BOOLEAN(),
               nullable=False)
    op.drop_index('ix_projects_id', table_name='projects')
    op.alter_column('subscriptions', 'features',
               existing_type=sa.TEXT(),
               type_=sqlmodel.sql.sqltypes.AutoString(),
               existing_nullable=True)
    op.alter_column('subscriptions', 'auto_renew',
               existing_type=sa.BOOLEAN(),
               nullable=False)
    op.drop_index('ix_subscriptions_id', table_name='subscriptions')
    op.alter_column('tasks', 'description',
               existing_type=sa.TEXT(),
               type_=sqlmodel.sql.sqltypes.AutoString(),
               existing_nullable=True)
    op.drop_index('ix_tasks_id', table_name='tasks')
    op.alter_column('users', 'access_token',
               existing_type=sa.TEXT(),
               type_=sqlmodel.sql.sqltypes.AutoString(),
               existing_nullable=True)
    op.alter_column('users', 'is_active',
               existing_type=sa.BOOLEAN(),
               nullable=False)
    op.alter_column('users', 'is_verified',
               existing_type=sa.BOOLEAN(),
               nullable=False)
    op.drop_index('ix_users_id', table_name='users')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index('ix_users_id', 'users', ['id'], unique=False)
    op.alter_column('users', 'is_verified',
               existing_type=sa.BOOLEAN(),
               nullable=True)
    op.alter_column('users', 'is_active',
               existing_type=sa.BOOLEAN(),
               nullable=True)
    op.alter_column('users', 'access_token',
               existing_type=sqlmodel.sql.sqltypes.AutoString(),
               type_=sa.TEXT(),
               existing_nullable=True)
    op.create_index('ix_tasks_id', 'tasks', ['id'], unique=False)
    op.alter_column('tasks', 'description',
               existing_type=sqlmodel.sql.sqltypes.AutoString(),
               type_=sa.TEXT(),
               existing_nullable=True)
    op.create_index('ix_subscriptions_id', 'subscriptions', ['id'], unique=False)
    op.alter_column('subscriptions', 'auto_renew',
               existing_type=sa.BOOLEAN(),
               nullable=True)
    op.alter_column('subscriptions', 'features',
               existing_type=sqlmodel.sql.sqltypes.AutoString(),
               type_=sa.TEXT(),
               existing_nullable=True)
    op.create_index('ix_projects_id', 'projects', ['id'], unique=False)
    op.alter_column('projects', 'is_archived',
               existing_type=sa.BOOLEAN(),
               nullable=True)
    op.alter_column('projects', 'is_public',
               existing_type=sa.BOOLEAN(),
               nullable=True)
    op.alter_column('projects', 'description',
               existing_type=sqlmodel.sql.sqltypes.AutoString(),
               type_=sa.TEXT(),
               existing_nullable=True)
    op.create_index('ix_project_members_id', 'project_members', ['id'], unique=False)
    op.alter_column('project_members', 'can_assign_tasks',
               existing_type=sa.BOOLEAN(),
               nullable=True)
    op.alter_column('project_members', 'can_create_tasks',
               existing_type=sa.BOOLEAN(),
               nullable=True)
    op.alter_column('project_members', 'can_view_all_boards',
               existing_type=sa.BOOLEAN(),
               nullable=True)
    op.alter_column('project_members', 'can_manage_members',
               existing_type=sa.BOOLEAN(),
               nullable=True)
    op.alter_column('project_members', 'can_delete_project',
               existing_type=sa.BOOLEAN(),
               nullable=True)
    op.alter_column('project_members', 'can_edit_project',
               existing_type=sa.BOOLEAN(),
               nullable=True)
    op.alter_column('project_members', 'can_create_boards',
               existing_type=sa.BOOLEAN(),
               nullable=True)
    op.create_index('ix_payments_id', 'payments', ['id'], unique=False)
    op.alter_column('payments', 'payment_metadata',
               existing_type=sqlmodel.sql.sqltypes.AutoString(),
               type_=sa.TEXT(),
               existing_nullable=True)
    op.alter_column('payments', 'memo',
               existing_type=sqlmodel.sql.sqltypes.AutoString(),
               type_=sa.TEXT(),
               existing_nullable=True)
    op.drop_constraint(None, 'boards', type_='foreignkey')
    op.create_index('ix_boards_id', 'boards', ['id'], unique=False)
    op.alter_column('boards', 'is_archived',
               existing_type=sa.BOOLEAN(),
               nullable=True)
    op.alter_column('boards', 'is_public',
               existing_type=sa.BOOLEAN(),
               nullable=True)
    op.alter_column('boards', 'description',
               existing_type=sqlmodel.sql.sqltypes.AutoString(),
               type_=sa.TEXT(),
               existing_nullable=True)
    op.create_index('ix_board_members_id', 'board_members', ['id'], unique=False)
    op.alter_column('board_members', 'can_invite',
               existing_type=sa.BOOLEAN(),
               nullable=True)
    op.alter_column('board_members', 'can_delete',
               existing_type=sa.BOOLEAN(),
               nullable=True)
    op.alter_column('board_members', 'can_edit',
               existing_type=sa.BOOLEAN(),
               nullable=True)
    op.alter_column('board_members', 'role',
               existing_type=sa.VARCHAR(length=50),
               nullable=True)
    # ### end Alembic commands ###
