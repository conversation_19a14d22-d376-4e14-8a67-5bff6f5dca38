# Plover Project Management Tool - Backend

A blockchain-based project management tool built on the Pi Network with FastAPI.

## Features

- 🔐 **Pi Network Authentication** - Secure user authentication using Pi Network
- 📋 **Task Management** - Create, assign, and track tasks with status updates
- 👥 **Team Collaboration** - Board-based collaboration with member management
- 💰 **Pi Payments** - Subscription management with Pi cryptocurrency
- 🚀 **High Performance** - Async FastAPI with PostgreSQL
- 🔒 **Security** - Rate limiting, CORS, security headers
- 📚 **Auto Documentation** - Interactive API docs with Swagger/ReDoc
- 🐳 **Docker Ready** - Complete containerization setup

## Tech Stack

- **Framework**: FastAPI (Python 3.11+)
- **Database**: PostgreSQL (Production) / SQLite (Development)
- **Authentication**: Pi Network SDK + JWT
- **ORM**: SQLAlchemy (Async)
- **Migrations**: Alembic
- **Testing**: Pytest
- **Containerization**: Docker & Docker Compose

## Quick Start

### Prerequisites

- Python 3.11+
- <PERSON><PERSON> & Docker Compose (for containerized deployment)
- Pi Network Developer Account

### 1. <PERSON><PERSON> and Setup

```bash
git clone <repository-url>
cd plover-pm-tool/backend
```

### 2. Environment Configuration

```bash
cp .env.example .env
# Edit .env with your configuration
```

Required environment variables:
- `PI_API_KEY`: Your Pi Network API key
- `SECRET_KEY`: JWT secret key (generate a secure random string)
- `DATABASE_URL`: Database connection string

### 3. Development Setup

#### Option A: Local Development

```bash
# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Initialize database
python scripts/dev.py setup

# Run development server
uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

#### Option B: Docker Development

```bash
# Build and start services
docker-compose up -d

# Initialize database with sample data
docker-compose exec backend python scripts/dev.py setup
```

### 4. Access the API

- **API Base URL**: http://localhost:8000
- **Interactive Docs**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc
- **Health Check**: http://localhost:8000/health

## API Endpoints

### Authentication
- `POST /api/v1/auth/signin` - Pi Network sign-in
- `GET /api/v1/auth/me` - Get current user
- `POST /api/v1/auth/refresh` - Refresh Pi token
- `POST /api/v1/auth/signout` - Sign out

### Tasks
- `GET /api/v1/tasks/` - List tasks
- `POST /api/v1/tasks/` - Create task
- `GET /api/v1/tasks/{id}` - Get task details
- `PUT /api/v1/tasks/{id}` - Update task
- `DELETE /api/v1/tasks/{id}` - Delete task

### Boards
- `GET /api/v1/boards/` - List boards
- `POST /api/v1/boards/` - Create board
- `GET /api/v1/boards/{id}` - Get board details
- `PUT /api/v1/boards/{id}` - Update board
- `POST /api/v1/boards/{id}/members` - Add member

### Payments
- `GET /api/v1/payments/plans` - Get subscription plans
- `POST /api/v1/payments/approve` - Approve payment
- `POST /api/v1/payments/complete` - Complete payment
- `GET /api/v1/payments/subscriptions` - Get user subscriptions

## Database Schema

### Core Models

- **User**: Pi Network user with roles and permissions
- **Task**: Project tasks with status, priority, and assignments
- **Board**: Collaboration boards with member management
- **Payment**: Pi Network payment tracking
- **Subscription**: User subscription management

### Relationships

- Users can create and be assigned to tasks
- Boards have multiple members with different roles
- Payments are linked to subscriptions
- Tasks can be organized within boards

## Development

### Database Migrations

```bash
# Create migration
alembic revision --autogenerate -m "Description"

# Apply migrations
alembic upgrade head

# Rollback migration
alembic downgrade -1
```

### Development Scripts

```bash
# Initialize database
python scripts/dev.py init-db

# Reset database
python scripts/dev.py reset-db

# Create sample data
python scripts/dev.py sample-data

# Full setup (reset + sample data)
python scripts/dev.py setup
```

### Testing

```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=app

# Run specific test file
pytest tests/test_auth.py

# Run with verbose output
pytest -v
```

### Code Quality

```bash
# Format code
black .

# Sort imports
isort .

# Lint code
flake8 .

# Type checking
mypy .
```

## Deployment

### Production Deployment

1. **Update Environment Variables**
   ```bash
   cp .env.example .env
   # Set production values
   ```

2. **Deploy with Docker**
   ```bash
   ./scripts/deploy.sh
   ```

3. **SSL Configuration**
   - Update nginx.conf for HTTPS
   - Add SSL certificates to ./ssl/ directory

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `ENVIRONMENT` | Environment (development/production) | development |
| `SECRET_KEY` | JWT secret key | Required |
| `PI_API_KEY` | Pi Network API key | Required |
| `DATABASE_URL` | Database connection string | SQLite |
| `ALLOWED_ORIGINS` | CORS allowed origins | localhost:3000 |
| `RATE_LIMIT_CALLS` | Rate limit requests per period | 100 |
| `RATE_LIMIT_PERIOD` | Rate limit period in seconds | 60 |

## Pi Network Integration

### Authentication Flow

1. Frontend uses Pi SDK to authenticate user
2. Backend verifies access token with Pi Platform API
3. JWT token issued for subsequent requests
4. User data stored in local database

### Payment Flow

1. Frontend creates payment with Pi SDK
2. Backend approves payment with Pi Platform
3. User completes payment on Pi blockchain
4. Backend completes payment and activates subscription

### Subscription Plans

- **Free Trial**: 1 month, basic features
- **Basic**: 5 Pi/month, essential features
- **Standard**: 15 Pi/month, advanced features
- **Premium**: 25 Pi/month, full features

## Security

- **Authentication**: Pi Network + JWT tokens
- **Rate Limiting**: Configurable per endpoint
- **CORS**: Configurable allowed origins
- **Security Headers**: XSS, CSRF, clickjacking protection
- **Input Validation**: Pydantic schemas
- **SQL Injection**: SQLAlchemy ORM protection

## Monitoring

- **Health Checks**: `/health` endpoint
- **Logging**: Structured logging with rotation
- **Metrics**: Request timing and status codes
- **Error Tracking**: Detailed error logs

