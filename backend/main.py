"""
Plover Project Management Tool - FastAPI Backend
Main application entry point
"""

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from contextlib import asynccontextmanager
import uvicorn
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Import routers
from app.api.v1.auth import router as auth_router
from app.api.v1.tasks import router as tasks_router
from app.api.v1.boards import router as boards_router
from app.api.v1.projects import router as projects_router
from app.api.v1.payments import router as payments_router
from app.api.v1.users import router as users_router
from app.api.v1.support import router as support_router

# Import database
from app.database.connection import init_db, close_db

# Import security middleware
from app.core.security import RateLimitMiddleware, SecurityHeadersMiddleware, LoggingMiddleware
from app.core.logging import app_logger
from app.core.config import get_cors_origins, get_allowed_hosts


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    # Startup
    await init_db()
    yield
    # Shutdown
    await close_db()


# Create FastAPI application
app = FastAPI(
    title="Plover Project Management Tool API",
    description="""
    A blockchain-based project management tool built on the Pi Network.

    ## Features

    * **Task Management**: Create, assign, and track tasks
    * **Team Collaboration**: Manage boards and team members
    * **Pi Network Integration**: Secure authentication and payments
    * **Subscription Management**: Flexible subscription plans

    ## Authentication

    This API uses Pi Network authentication. To authenticate:
    1. Use the Pi SDK on the frontend to get an access token
    2. Call `/api/v1/auth/signin` with the Pi authentication result
    3. Use the returned JWT token for subsequent API calls

    ## Payment Flow

    1. **Create Payment**: Frontend creates payment with Pi SDK
    2. **Approve Payment**: Backend approves payment with Pi Platform
    3. **Complete Payment**: Backend completes payment after blockchain confirmation

    ## Subscription Plans

    * **Free Trial**: 1 month free access
    * **Basic Plan**: 5 Pi/month - Essential features
    * **Standard Plan**: 15 Pi/month - Advanced features
    * **Premium Plan**: 25 Pi/month - Full features
    """,
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan,
    contact={
        "name": "Plover Development Team",
        "email": "<EMAIL>",
    },
)

# CORS middleware
print("Allowed origins:", get_cors_origins())
app.add_middleware(
    CORSMiddleware,
    allow_origins=get_cors_origins(),
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "PATCH", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)

# Trusted host middleware
allowed_hosts = get_allowed_hosts()
if "*" not in allowed_hosts:  # Only add trusted host middleware if not allowing all hosts
    app.add_middleware(
        TrustedHostMiddleware,
        allowed_hosts=allowed_hosts
    )

# Security middleware
app.add_middleware(SecurityHeadersMiddleware)
app.add_middleware(LoggingMiddleware)

# Rate limiting middleware (100 requests per minute by default)
app.add_middleware(
    RateLimitMiddleware,
    calls=int(os.getenv("RATE_LIMIT_CALLS", 100)),
    period=int(os.getenv("RATE_LIMIT_PERIOD", 60))
)

# Include API routers
app.include_router(auth_router, prefix="/api/v1/auth", tags=["Authentication"])
app.include_router(users_router, prefix="/api/v1/users", tags=["Users"])
app.include_router(projects_router, prefix="/api/v1/projects", tags=["Projects"])
app.include_router(tasks_router, prefix="/api/v1/tasks", tags=["Tasks"])
app.include_router(boards_router, prefix="/api/v1/boards", tags=["Boards"])
app.include_router(payments_router, prefix="/api/v1/payments", tags=["Payments"])
app.include_router(support_router, prefix="/api/v1/support", tags=["Support"])


@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "Welcome to Plover Project Management Tool API",
        "version": "1.0.0",
        "docs": "/docs"
    }


@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "service": "plover-pm-api"}


if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host=os.getenv("HOST", "0.0.0.0"),
        port=int(os.getenv("PORT", 8000)),
        reload=os.getenv("ENVIRONMENT", "development") == "development"
    )
