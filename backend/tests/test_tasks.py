"""
Task management API tests
"""

import pytest
from httpx import Async<PERSON><PERSON>
from sqlalchemy.ext.asyncio import AsyncSession

from app.models.user import User
from app.models.task import Task, TaskStatus, TaskPriority


class TestTaskAPI:
    """Test task management endpoints"""
    
    @pytest.mark.asyncio
    async def test_create_task(self, client: AsyncClient, auth_headers: dict):
        """Test task creation"""
        task_data = {
            "title": "Test Task",
            "description": "This is a test task",
            "priority": "medium",
            "due_date": "2024-12-31T23:59:59"
        }
        
        response = await client.post("/api/v1/tasks/", json=task_data, headers=auth_headers)
        
        assert response.status_code == 201
        data = response.json()
        assert data["title"] == task_data["title"]
        assert data["description"] == task_data["description"]
        assert data["status"] == "todo"
    
    @pytest.mark.asyncio
    async def test_list_tasks(self, client: AsyncClient, auth_headers: dict):
        """Test listing tasks"""
        response = await client.get("/api/v1/tasks/", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
    
    @pytest.mark.asyncio
    async def test_get_task(self, client: AsyncClient, auth_headers: dict, test_user: User, db_session: AsyncSession):
        """Test getting a specific task"""
        # Create a test task
        task = Task(
            title="Test Task",
            description="Test description",
            creator_id=test_user.id,
            status=TaskStatus.TODO,
            priority=TaskPriority.MEDIUM
        )
        db_session.add(task)
        await db_session.commit()
        await db_session.refresh(task)
        
        response = await client.get(f"/api/v1/tasks/{task.id}", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == task.id
        assert data["title"] == task.title
    
    @pytest.mark.asyncio
    async def test_update_task(self, client: AsyncClient, auth_headers: dict, test_user: User, db_session: AsyncSession):
        """Test updating a task"""
        # Create a test task
        task = Task(
            title="Original Title",
            description="Original description",
            creator_id=test_user.id,
            status=TaskStatus.TODO,
            priority=TaskPriority.MEDIUM
        )
        db_session.add(task)
        await db_session.commit()
        await db_session.refresh(task)
        
        update_data = {
            "title": "Updated Title",
            "status": "in_progress"
        }
        
        response = await client.put(f"/api/v1/tasks/{task.id}", json=update_data, headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["title"] == update_data["title"]
        assert data["status"] == update_data["status"]
    
    @pytest.mark.asyncio
    async def test_delete_task(self, client: AsyncClient, auth_headers: dict, test_user: User, db_session: AsyncSession):
        """Test deleting a task"""
        # Create a test task
        task = Task(
            title="Task to Delete",
            description="This task will be deleted",
            creator_id=test_user.id,
            status=TaskStatus.TODO,
            priority=TaskPriority.MEDIUM
        )
        db_session.add(task)
        await db_session.commit()
        await db_session.refresh(task)
        
        response = await client.delete(f"/api/v1/tasks/{task.id}", headers=auth_headers)
        
        assert response.status_code == 204
    
    @pytest.mark.asyncio
    async def test_unauthorized_task_access(self, client: AsyncClient):
        """Test unauthorized access to task endpoints"""
        response = await client.get("/api/v1/tasks/")
        assert response.status_code == 401
        
        response = await client.post("/api/v1/tasks/", json={"title": "Test"})
        assert response.status_code == 401
