"""
Authentication API tests
"""

import pytest
from httpx import <PERSON><PERSON><PERSON><PERSON>
from sqlalchemy.ext.asyncio import AsyncSession

from app.models.user import User


class TestAuthAPI:
    """Test authentication endpoints"""
    
    @pytest.mark.asyncio
    async def test_signin_success(self, client: AsyncClient, db_session: AsyncSession):
        """Test successful Pi Network sign-in"""
        signin_data = {
            "authResult": {
                "accessToken": "test_pi_access_token",
                "user": {
                    "uid": "test_pi_uid",
                    "username": "testpiuser",
                    "roles": ["user"]
                }
            }
        }
        
        # Mock Pi Platform API response
        # Note: In real tests, you'd mock the pi_client.verify_user method
        
        response = await client.post("/api/v1/auth/signin", json=signin_data)
        
        # This will fail without proper mocking, but shows the test structure
        # assert response.status_code == 200
        # data = response.json()
        # assert "access_token" in data
        # assert data["token_type"] == "bearer"
    
    @pytest.mark.asyncio
    async def test_get_current_user(self, client: AsyncClient, auth_headers: dict):
        """Test getting current user information"""
        response = await client.get("/api/v1/auth/me", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert "uid" in data
        assert "username" in data
    
    @pytest.mark.asyncio
    async def test_unauthorized_access(self, client: AsyncClient):
        """Test unauthorized access to protected endpoint"""
        response = await client.get("/api/v1/auth/me")
        
        assert response.status_code == 401
    
    @pytest.mark.asyncio
    async def test_invalid_token(self, client: AsyncClient):
        """Test access with invalid token"""
        headers = {"Authorization": "Bearer invalid_token"}
        response = await client.get("/api/v1/auth/me", headers=headers)

        assert response.status_code == 401

    @pytest.mark.asyncio
    async def test_signin_missing_data(self, client: AsyncClient):
        """Test signin with missing data"""
        response = await client.post("/api/v1/auth/signin", json={})
        assert response.status_code == 422  # Validation error

    @pytest.mark.asyncio
    async def test_signin_invalid_structure(self, client: AsyncClient):
        """Test signin with invalid data structure"""
        response = await client.post("/api/v1/auth/signin", json={
            "accessToken": "invalid_token",
            "user": {
                "uid": "test_uid",
                "username": "testuser"
            }
        })
        # Should return error for invalid token structure
        assert response.status_code in [400, 401, 422]

    @pytest.mark.asyncio
    async def test_signout_without_auth(self, client: AsyncClient):
        """Test signout without authentication"""
        response = await client.post("/api/v1/auth/signout")
        assert response.status_code == 401  # Unauthorized
