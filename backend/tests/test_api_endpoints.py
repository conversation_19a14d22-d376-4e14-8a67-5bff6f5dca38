"""
Test API endpoints without authentication
"""

import pytest
import sys
import os
from pathlib import Path
from httpx import AsyncClient

# Add the backend directory to the Python path
backend_dir = Path(__file__).parent.parent
sys.path.insert(0, str(backend_dir))

from main import app


@pytest.mark.asyncio
async def test_auth_endpoints_exist():
    """Test that auth endpoints exist and return proper error codes"""
    async with AsyncClient(app=app, base_url="http://test") as ac:
        # Test signin endpoint
        response = await ac.post("/api/v1/auth/signin", json={})
        assert response.status_code in [400, 401, 422]  # Should not be 404
        
        # Test me endpoint
        response = await ac.get("/api/v1/auth/me")
        assert response.status_code in [401, 403]  # Unauthorized or Forbidden

        # Test signout endpoint
        response = await ac.post("/api/v1/auth/signout")
        assert response.status_code in [401, 403]  # Unauthorized or Forbidden


@pytest.mark.asyncio
async def test_task_endpoints_exist():
    """Test that task endpoints exist and return proper error codes"""
    async with Async<PERSON>lient(app=app, base_url="http://test") as ac:
        # Test list tasks
        response = await ac.get("/api/v1/tasks/")
        assert response.status_code in [401, 403]  # Should be unauthorized, not 404

        # Test create task
        response = await ac.post("/api/v1/tasks/", json={"title": "Test"})
        assert response.status_code in [401, 403]  # Should be unauthorized, not 404

        # Test get task
        response = await ac.get("/api/v1/tasks/1")
        assert response.status_code in [401, 403]  # Should be unauthorized, not 404

        # Test update task
        response = await ac.put("/api/v1/tasks/1", json={"title": "Updated"})
        assert response.status_code in [401, 403]  # Should be unauthorized, not 404

        # Test delete task
        response = await ac.delete("/api/v1/tasks/1")
        assert response.status_code in [401, 403]  # Should be unauthorized, not 404


@pytest.mark.asyncio
async def test_board_endpoints_exist():
    """Test that board endpoints exist and return proper error codes"""
    async with AsyncClient(app=app, base_url="http://test") as ac:
        # Test list boards
        response = await ac.get("/api/v1/boards/")
        assert response.status_code in [401, 403]  # Should be unauthorized, not 404

        # Test create board
        response = await ac.post("/api/v1/boards/", json={"name": "Test Board"})
        assert response.status_code in [401, 403]  # Should be unauthorized, not 404


@pytest.mark.asyncio
async def test_user_endpoints_exist():
    """Test that user endpoints exist and return proper error codes"""
    async with AsyncClient(app=app, base_url="http://test") as ac:
        # Test get users
        response = await ac.get("/api/v1/users/")
        assert response.status_code in [401, 403]  # Should be unauthorized, not 404

        # Test get user profile
        response = await ac.get("/api/v1/users/me")
        assert response.status_code in [401, 403]  # Should be unauthorized, not 404


@pytest.mark.asyncio
async def test_payment_endpoints_exist():
    """Test that payment endpoints exist and return proper error codes"""
    async with AsyncClient(app=app, base_url="http://test") as ac:
        # Test create payment
        response = await ac.post("/api/v1/payments/", json={"amount": 10.0})
        assert response.status_code in [400, 401, 403, 404, 422]  # May be 404 if endpoint not configured

        # Test get payments
        response = await ac.get("/api/v1/payments/")
        assert response.status_code in [401, 403, 404]  # May be 404 if endpoint not configured


@pytest.mark.asyncio
async def test_signin_data_validation():
    """Test signin endpoint data validation"""
    async with AsyncClient(app=app, base_url="http://test") as ac:
        # Test with empty data
        response = await ac.post("/api/v1/auth/signin", json={})
        assert response.status_code == 422  # Validation error
        
        # Test with invalid structure
        response = await ac.post("/api/v1/auth/signin", json={
            "invalid": "data"
        })
        assert response.status_code == 422  # Validation error


@pytest.mark.asyncio
async def test_task_data_validation():
    """Test task endpoint data validation"""
    async with AsyncClient(app=app, base_url="http://test") as ac:
        # Test create task with invalid data (should be 401/403 due to no auth, but structure is tested)
        response = await ac.post("/api/v1/tasks/", json={
            "invalid": "data"
        })
        assert response.status_code in [401, 403, 422]  # Unauthorized or validation error


@pytest.mark.asyncio
async def test_cors_preflight():
    """Test CORS preflight requests"""
    async with AsyncClient(app=app, base_url="http://test") as ac:
        # Test preflight request
        response = await ac.request(
            "OPTIONS", 
            "/api/v1/auth/signin",
            headers={
                "Origin": "http://localhost:3000",
                "Access-Control-Request-Method": "POST",
                "Access-Control-Request-Headers": "Content-Type"
            }
        )
        # Should return 200 for OPTIONS or 405 if not implemented
        assert response.status_code in [200, 405]
