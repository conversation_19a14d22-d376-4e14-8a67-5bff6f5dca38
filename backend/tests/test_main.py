"""
Test cases for main application endpoints
"""

import pytest
import sys
import os
from pathlib import Path
from httpx import AsyncClient

# Add the backend directory to the Python path
backend_dir = Path(__file__).parent.parent
sys.path.insert(0, str(backend_dir))

from main import app


@pytest.mark.asyncio
async def test_root_endpoint():
    """Test the root endpoint"""
    async with AsyncClient(app=app, base_url="http://test") as ac:
        response = await ac.get("/")
    
    assert response.status_code == 200
    data = response.json()
    assert data["message"] == "Welcome to Plover Project Management Tool API"
    assert data["version"] == "1.0.0"
    assert data["docs"] == "/docs"


@pytest.mark.asyncio
async def test_health_endpoint():
    """Test the health check endpoint"""
    async with AsyncClient(app=app, base_url="http://test") as ac:
        response = await ac.get("/health")
    
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "healthy"
    assert data["service"] == "plover-pm-api"


@pytest.mark.asyncio
async def test_docs_endpoint():
    """Test the docs endpoint returns HTML"""
    async with AsyncClient(app=app, base_url="http://test") as ac:
        response = await ac.get("/docs")
    
    assert response.status_code == 200
    assert "text/html" in response.headers["content-type"]
    assert "swagger-ui" in response.text.lower()


@pytest.mark.asyncio
async def test_redoc_endpoint():
    """Test the redoc endpoint returns HTML"""
    async with AsyncClient(app=app, base_url="http://test") as ac:
        response = await ac.get("/redoc")
    
    assert response.status_code == 200
    assert "text/html" in response.headers["content-type"]


@pytest.mark.asyncio
async def test_openapi_json():
    """Test the OpenAPI JSON endpoint"""
    async with AsyncClient(app=app, base_url="http://test") as ac:
        response = await ac.get("/openapi.json")
    
    assert response.status_code == 200
    data = response.json()
    assert data["info"]["title"] == "Plover Project Management Tool API"
    assert data["info"]["version"] == "1.0.0"


@pytest.mark.asyncio
async def test_cors_headers():
    """Test CORS headers are present"""
    async with AsyncClient(app=app, base_url="http://test") as ac:
        response = await ac.get("/", headers={"Origin": "http://localhost:3000"})

    assert response.status_code == 200
    # Check if CORS headers are present (they might be lowercase)
    headers_lower = {k.lower(): v for k, v in response.headers.items()}
    assert "access-control-allow-origin" in headers_lower or "Access-Control-Allow-Origin" in response.headers


@pytest.mark.asyncio
async def test_invalid_endpoint():
    """Test invalid endpoint returns 404"""
    async with AsyncClient(app=app, base_url="http://test") as ac:
        response = await ac.get("/invalid-endpoint")
    
    assert response.status_code == 404
