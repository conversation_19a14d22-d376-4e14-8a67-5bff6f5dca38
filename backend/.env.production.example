# Production Environment Configuration for Backend

# Application Configuration
APP_NAME="Plover Project Management Tool"
APP_VERSION="1.0.0"
ENVIRONMENT="production"
DEBUG=false
HOST="0.0.0.0"
PORT=8000

# Security Configuration
SECRET_KEY="your-super-secret-key-min-32-chars-change-this-in-production"
ACCESS_TOKEN_EXPIRE_MINUTES=30
ALGORITHM="HS256"

# CORS Configuration - Update with your domain
ALLOWED_ORIGINS="https://yourdomain.com,https://www.yourdomain.com"
ALLOWED_HOSTS="yourdomain.com,www.yourdomain.com"

# Database Configuration (PostgreSQL for production)
DATABASE_URL="postgresql+asyncpg://plover_user:secure_password_change_this@postgres:5432/plover_pm"
DATABASE_ECHO=false

# Redis Configuration
REDIS_URL="redis://redis:6379"

# Pi Network Integration
PI_API_KEY="your-production-pi-api-key-from-pi-developer-portal"
PI_PLATFORM_API_URL="https://api.minepi.com"
PI_SANDBOX_MODE=false

# Frontend Configuration
FRONTEND_URL="https://yourdomain.com"

# Logging Configuration
LOG_LEVEL="INFO"

# Email Configuration (Optional - for notifications)
SMTP_HOST="smtp.gmail.com"
SMTP_PORT=587
SMTP_USERNAME="<EMAIL>"
SMTP_PASSWORD="your-app-password"
SMTP_FROM_EMAIL="<EMAIL>"

# Monitoring (Optional)
SENTRY_DSN=""
PROMETHEUS_ENABLED=false
