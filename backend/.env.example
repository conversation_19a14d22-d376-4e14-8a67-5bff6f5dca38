# Application Configuration
APP_NAME="Plover Project Management Tool"
APP_VERSION="1.0.0"
ENVIRONMENT="development"  # development, staging, production
DEBUG=true
HOST="0.0.0.0"
PORT=8000

# Security
SECRET_KEY="your-super-secret-key-change-in-production-min-32-chars"
ACCESS_TOKEN_EXPIRE_MINUTES=30
ALGORITHM="HS256"

# CORS Configuration
ALLOWED_ORIGINS="http://localhost:3000,http://127.0.0.1:3000"
ALLOWED_HOSTS="localhost,127.0.0.1"

# Database Configuration
# Development (SQLite)
DATABASE_URL="sqlite+aiosqlite:///./plover_pm.db"
# Production (PostgreSQL)
# DATABASE_URL="postgresql+asyncpg://username:password@localhost:5432/plover_pm"
DATABASE_ECHO=false

# Pi Network Integration
PI_API_KEY="xxwkfgs6gpe1uvzw797yoz8yaysqxwvvtilzykuwcuebykswknldhwylgafarlck"
PI_PLATFORM_API_URL="https://api.minepi.com"
PI_SANDBOX_MODE=true  # Use sandbox for development, false for production

# Frontend Configuration
FRONTEND_URL="http://localhost:3000"

# Logging
LOG_LEVEL="INFO"  # DEBUG, INFO, WARNING, ERROR, CRITICAL
