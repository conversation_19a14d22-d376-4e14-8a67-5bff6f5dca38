-- Initialize PostgreSQL database for Plover Project Management Tool

-- Create database (if not exists)
-- This is handled by docker-compose environment variables

-- Create extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- Create indexes for better performance (will be created by Alembic migrations)
-- This file is mainly for any initial setup that needs to happen before the application starts
