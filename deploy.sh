#!/bin/bash

# Plover Project Management Tool - Deployment Script
# This script helps deploy the application to production

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as root
if [[ $EUID -eq 0 ]]; then
   log_error "This script should not be run as root for security reasons"
   exit 1
fi

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    log_error "Docker is not installed. Please install Docker first."
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
    log_error "Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

# Main deployment function
deploy() {
    log_info "Starting Plover deployment..."

    # Check if production environment files exist
    if [[ ! -f "backend/.env.production" ]]; then
        log_warning "backend/.env.production not found. Creating from example..."
        cp backend/.env.production.example backend/.env.production
        log_warning "Please edit backend/.env.production with your production settings"
        read -p "Press Enter to continue after editing the file..."
    fi

    if [[ ! -f "frontend/.env.production" ]]; then
        log_warning "frontend/.env.production not found. Creating from example..."
        cp frontend/.env.production.example frontend/.env.production
        log_warning "Please edit frontend/.env.production with your production settings"
        read -p "Press Enter to continue after editing the file..."
    fi

    # Create necessary directories
    log_info "Creating necessary directories..."
    mkdir -p nginx/logs
    mkdir -p ssl
    mkdir -p backend/logs

    # Stop existing containers
    log_info "Stopping existing containers..."
    docker compose -f docker-compose.prod.yml down || true

    # Build and start containers
    log_info "Building and starting containers..."
    docker compose -f docker-compose.prod.yml up -d --build

    # Wait for services to be ready
    log_info "Waiting for services to be ready..."
    sleep 30

    # Check if services are running
    if docker compose -f docker-compose.prod.yml ps | grep -q "Up"; then
        log_success "Deployment completed successfully!"
        log_info "Services status:"
        docker compose -f docker-compose.prod.yml ps
        
        log_info "You can now access your application at:"
        log_info "- HTTP: http://your-domain.com"
        log_info "- HTTPS: https://your-domain.com (if SSL is configured)"
        
        log_info "To view logs, run:"
        log_info "docker compose -f docker-compose.prod.yml logs -f"
    else
        log_error "Deployment failed. Check the logs:"
        docker compose -f docker-compose.prod.yml logs
        exit 1
    fi
}

# SSL setup function
setup_ssl() {
    log_info "Setting up SSL with Let's Encrypt..."
    
    read -p "Enter your domain name (e.g., yourdomain.com): " DOMAIN
    read -p "Enter your email for Let's Encrypt: " EMAIL
    
    if [[ -z "$DOMAIN" || -z "$EMAIL" ]]; then
        log_error "Domain and email are required"
        exit 1
    fi
    
    # Install certbot if not installed
    if ! command -v certbot &> /dev/null; then
        log_info "Installing certbot..."
        sudo apt update
        sudo apt install -y certbot python3-certbot-nginx
    fi
    
    # Stop nginx temporarily
    docker compose -f docker-compose.prod.yml stop nginx
    
    # Get certificate
    sudo certbot certonly --standalone -d $DOMAIN -d www.$DOMAIN --email $EMAIL --agree-tos --non-interactive
    
    # Copy certificates
    sudo cp /etc/letsencrypt/live/$DOMAIN/fullchain.pem ssl/
    sudo cp /etc/letsencrypt/live/$DOMAIN/privkey.pem ssl/
    sudo chown $USER:$USER ssl/*.pem
    
    # Update nginx config with domain
    sed -i "s/yourdomain.com/$DOMAIN/g" nginx/nginx.conf
    
    # Restart nginx
    docker compose -f docker-compose.prod.yml start nginx
    
    log_success "SSL setup completed!"
}

# Backup function
backup() {
    log_info "Creating backup..."
    
    BACKUP_DIR="backups/$(date +%Y%m%d_%H%M%S)"
    mkdir -p $BACKUP_DIR
    
    # Backup database
    docker compose -f docker-compose.prod.yml exec -T postgres pg_dump -U plover_user plover_pm > $BACKUP_DIR/database.sql
    
    # Backup environment files
    cp backend/.env.production $BACKUP_DIR/
    cp frontend/.env.production $BACKUP_DIR/
    
    # Backup SSL certificates
    if [[ -d "ssl" ]]; then
        cp -r ssl $BACKUP_DIR/
    fi
    
    log_success "Backup created at $BACKUP_DIR"
}

# Show usage
usage() {
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  deploy    Deploy the application"
    echo "  ssl       Setup SSL certificates"
    echo "  backup    Create a backup"
    echo "  logs      Show application logs"
    echo "  status    Show services status"
    echo "  stop      Stop all services"
    echo "  restart   Restart all services"
    echo ""
}

# Parse command line arguments
case "${1:-}" in
    deploy)
        deploy
        ;;
    ssl)
        setup_ssl
        ;;
    backup)
        backup
        ;;
    logs)
        docker compose -f docker-compose.prod.yml logs -f
        ;;
    status)
        docker compose -f docker-compose.prod.yml ps
        ;;
    stop)
        docker compose -f docker-compose.prod.yml down
        ;;
    restart)
        docker compose -f docker-compose.prod.yml restart
        ;;
    *)
        usage
        exit 1
        ;;
esac
