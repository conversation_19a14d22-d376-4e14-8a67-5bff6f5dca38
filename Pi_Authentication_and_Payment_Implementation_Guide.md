# Pi Network Authentication and Payment Implementation Guide

This document explains how authentication and payment systems work in Pi Network applications, providing a framework-agnostic guide for implementing similar functionality in any application (Next.js, FastAPI, Django, etc.).

## Overview

Pi Network applications require implementation of:
1. **User Authentication** using Pi SDK and Pi Platform API
2. **Payment Processing** with Pi cryptocurrency
3. **Session/State Management** (any method you prefer)
4. **Database Integration** (any database you prefer)

## Core Concepts

### Authentication Flow
1. **Frontend**: Use Pi SDK to get user access token
2. **Backend**: Verify access token with Pi Platform API
3. **Database**: Store user data in your own database
4. **Session**: Maintain user session using your preferred method

### Payment Flow
1. **Frontend**: Create payment using Pi SDK with callbacks
2. **Backend**: Handle payment approval, completion, and cancellation
3. **Database**: Track orders and payment status
4. **Blockchain**: Verify transactions on Pi blockchain

## Available User Data

### What User Information Can You Access?

From the Pi SDK authentication, you can access:

```javascript
// Available scopes and their data
const scopes = ['username', 'payments'];
const authResult = await window.Pi.authenticate(scopes, onIncompletePaymentFound);

// AuthResult contains:
{
  accessToken: "user_access_token_string",
  user: {
    uid: "unique_pi_user_id",           // Unique Pi Network user identifier
    username: "pi_username",           // Pi Network username
    roles: ["role1", "role2"]          // User roles (if any)
  }
}
```

### Important Notes About User Data:
- **No Email Access**: Pi Network does NOT provide user email addresses through their API
- **Username Only**: You only get the Pi Network username, not real names
- **Privacy-Focused**: Pi Network prioritizes user privacy, so personal data is limited
- **UID is Key**: Use the `uid` as the primary identifier for users in your database

### What You CAN Store in Your Database:
- Pi Network UID (primary key)
- Pi Network username
- User roles
- Your own app-specific user data (preferences, settings, etc.)
- Order history and payment records

### What You CANNOT Get:
- Email addresses
- Real names
- Phone numbers
- Other personal information

## Authentication Implementation

### 1. Frontend Authentication (Any Framework)

#### Pi SDK Integration
```html
<!-- Include Pi SDK in your HTML -->
<script src="https://sdk.minepi.com/pi-sdk.js"></script>

<script>
// Initialize Pi SDK
Pi.init({ version: "2.0", sandbox: runSDKInSandboxMode });
</script>
```

#### User Sign-In Process (Framework Agnostic)
```javascript
const signIn = async () => {
  const scopes = ['username', 'payments'];
  const authResult = await window.Pi.authenticate(scopes, onIncompletePaymentFound);

  // Send auth result to your backend for verification
  await fetch('/api/auth/signin', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ authResult })
  });

  // Update your app state
  setUser(authResult.user);
}
```

### 2. Backend Authentication (Any Framework)

#### Pi Platform API Client Setup
```python
# Example for FastAPI/Python
import httpx

class PiPlatformClient:
    def __init__(self, api_key: str):
        self.base_url = "https://api.minepi.com"
        self.headers = {"Authorization": f"Key {api_key}"}

    async def verify_user(self, access_token: str):
        async with httpx.AsyncClient() as client:
            response = await client.get(
                f"{self.base_url}/v2/me",
                headers={"Authorization": f"Bearer {access_token}"}
            )
            if response.status_code == 200:
                return response.json()
            else:
                raise Exception("Invalid access token")
```

```javascript
// Example for Node.js/Express
const axios = require('axios');

const piPlatformClient = axios.create({
  baseURL: 'https://api.minepi.com',
  timeout: 20000,
  headers: { 'Authorization': `Key ${process.env.PI_API_KEY}` }
});
```

#### Access Token Verification (Framework Agnostic Logic)
```python
# FastAPI example
@app.post("/api/auth/signin")
async def signin(auth_data: dict):
    try:
        # Verify access token with Pi Platform API
        user_data = await pi_client.verify_user(auth_data['authResult']['accessToken'])

        # Token is valid, proceed with user creation/update
        user = await create_or_update_user(auth_data['authResult'])
        return {"message": "User signed in", "user": user}
    except Exception:
        raise HTTPException(status_code=401, detail="Invalid access token")
```

```javascript
// Express.js example
app.post('/api/auth/signin', async (req, res) => {
  try {
    const auth = req.body.authResult;

    // Verify access token with Pi Platform API
    const me = await piPlatformClient.get('/v2/me', {
      headers: { 'Authorization': `Bearer ${auth.accessToken}` }
    });

    // Token is valid, proceed with user creation/update
    const user = await createOrUpdateUser(auth);
    res.json({ message: "User signed in", user });
  } catch (err) {
    res.status(401).json({error: "Invalid access token"});
  }
});
```

#### User Data Management (Database Agnostic)
```python
# Example user management logic (adaptable to any database)
async def create_or_update_user(auth_result):
    user_data = auth_result['user']

    # Check if user exists (using your preferred database)
    existing_user = await db.find_user_by_uid(user_data['uid'])

    if existing_user:
        # Update existing user's access token
        await db.update_user(existing_user.id, {
            'access_token': auth_result['accessToken']
        })
        return existing_user
    else:
        # Create new user
        new_user = await db.create_user({
            'uid': user_data['uid'],
            'username': user_data['username'],
            'roles': user_data['roles'],
            'access_token': auth_result['accessToken'],
            'created_at': datetime.now()
        })
        return new_user
```

### 3. Session/State Management (Choose Your Method)

You can use any session management approach:

#### Option 1: Server Sessions (Express.js example)
```javascript
app.use(session({
  secret: process.env.SESSION_SECRET,
  resave: false,
  saveUninitialized: false,
  store: new MongoStore({ mongoUrl: process.env.MONGO_URL })
}));
```

#### Option 2: JWT Tokens
```python
# FastAPI with JWT
from jose import JWTError, jwt

def create_access_token(user_data: dict):
    return jwt.encode(user_data, SECRET_KEY, algorithm=ALGORITHM)

def verify_token(token: str):
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        return payload
    except JWTError:
        return None
```

#### Option 3: Next.js with NextAuth
```javascript
// pages/api/auth/[...nextauth].js
import NextAuth from 'next-auth'

export default NextAuth({
  providers: [
    // Custom Pi Network provider
    {
      id: "pi-network",
      name: "Pi Network",
      type: "oauth",
      // Custom authorization logic
    }
  ],
})
```

## Payment System Implementation

### 1. Frontend Payment Flow (Framework Agnostic)

#### Payment Initiation
```javascript
const orderProduct = async (memo, amount, paymentMetadata) => {
  if (!user) {
    // Redirect to login or show login modal
    return showLoginModal();
  }

  const paymentData = { amount, memo, metadata: paymentMetadata };
  const callbacks = {
    onReadyForServerApproval,
    onReadyForServerCompletion,
    onCancel,
    onError
  };

  const payment = await window.Pi.createPayment(paymentData, callbacks);
};
```

#### Payment Callbacks Implementation
```javascript
// 1. Server Approval Callback
const onReadyForServerApproval = (paymentId) => {
  fetch('/api/payments/approve', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ paymentId })
  });
}

// 2. Server Completion Callback
const onReadyForServerCompletion = (paymentId, txid) => {
  fetch('/api/payments/complete', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ paymentId, txid })
  });
}

// 3. Payment Cancellation
const onCancel = (paymentId) => {
  fetch('/api/payments/cancelled', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ paymentId })
  });
}

// 4. Error Handling
const onError = (error, payment) => {
  console.log("Payment error:", error);
  if (payment) {
    // Handle specific payment errors
    console.log("Payment details:", payment);
  }
}

// 5. Incomplete Payment Handling (called during authentication)
const onIncompletePaymentFound = (payment) => {
  // Called during authentication if user has incomplete payments
  fetch('/api/payments/incomplete', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ payment })
  });
}
```

### 2. Backend Payment Flow (Framework Agnostic)

#### Payment Approval Endpoint
```python
# FastAPI example
@app.post("/api/payments/approve")
async def approve_payment(payment_data: dict, current_user: User = Depends(get_current_user)):
    if not current_user:
        raise HTTPException(status_code=401, detail="Unauthorized")

    payment_id = payment_data['paymentId']

    # Get payment details from Pi Platform
    payment_info = await pi_client.get_payment(payment_id)

    # Create order record in your database
    order = await db.create_order({
        'pi_payment_id': payment_id,
        'product_id': payment_info['metadata']['productId'],
        'user_id': current_user.uid,
        'txid': None,
        'paid': False,
        'cancelled': False,
        'created_at': datetime.now()
    })

    # Approve payment with Pi Platform
    await pi_client.approve_payment(payment_id)
    return {"message": f"Approved payment {payment_id}"}
```

```javascript
// Express.js example
app.post('/api/payments/approve', async (req, res) => {
  if (!req.user) {
    return res.status(401).json({ error: 'unauthorized' });
  }

  const { paymentId } = req.body;

  // Get payment details from Pi Platform
  const paymentInfo = await piClient.get(`/v2/payments/${paymentId}`);

  // Create order record in database
  await db.orders.create({
    pi_payment_id: paymentId,
    product_id: paymentInfo.data.metadata.productId,
    user_id: req.user.uid,
    txid: null,
    paid: false,
    cancelled: false,
    created_at: new Date()
  });

  // Approve payment with Pi Platform
  await piClient.post(`/v2/payments/${paymentId}/approve`);
  res.json({ message: `Approved payment ${paymentId}` });
});
```

#### Payment Completion Endpoint
```python
# FastAPI example
@app.post("/api/payments/complete")
async def complete_payment(payment_data: dict):
    payment_id = payment_data['paymentId']
    txid = payment_data['txid']

    # Update order as paid in your database
    await db.update_order_by_payment_id(payment_id, {
        'txid': txid,
        'paid': True,
        'completed_at': datetime.now()
    })

    # Notify Pi Platform of completion
    await pi_client.complete_payment(payment_id, txid)
    return {"message": f"Completed payment {payment_id}"}
```

```javascript
// Express.js example
app.post('/api/payments/complete', async (req, res) => {
  const { paymentId, txid } = req.body;

  // Update order as paid
  await db.orders.update(
    { pi_payment_id: paymentId },
    { txid: txid, paid: true, completed_at: new Date() }
  );

  // Notify Pi Platform of completion
  await piClient.post(`/v2/payments/${paymentId}/complete`, { txid });
  res.json({ message: `Completed payment ${paymentId}` });
});
```

#### Incomplete Payment Handling
```python
# FastAPI example
@app.post("/api/payments/incomplete")
async def handle_incomplete_payment(payment_data: dict):
    payment = payment_data['payment']
    payment_id = payment['identifier']
    txid = payment.get('transaction', {}).get('txid')
    tx_url = payment.get('transaction', {}).get('_link')

    # Find the incomplete order in your database
    order = await db.find_order_by_payment_id(payment_id)
    if not order:
        raise HTTPException(status_code=400, detail="Order not found")

    # Verify transaction on Pi blockchain
    async with httpx.AsyncClient() as client:
        response = await client.get(tx_url)
        blockchain_data = response.json()
        payment_id_on_block = blockchain_data['memo']

    if payment_id_on_block != order.pi_payment_id:
        raise HTTPException(status_code=400, detail="Payment ID doesn't match")

    # Mark order as paid and complete payment
    await db.update_order(order.id, {
        'txid': txid,
        'paid': True,
        'completed_at': datetime.now()
    })

    await pi_client.complete_payment(payment_id, txid)
    return {"message": f"Handled incomplete payment {payment_id}"}
```

```javascript
// Express.js example
app.post('/api/payments/incomplete', async (req, res) => {
  const { payment } = req.body;
  const paymentId = payment.identifier;
  const txid = payment.transaction?.txid;
  const txURL = payment.transaction?._link;

  // Find the incomplete order
  const order = await db.orders.findOne({ pi_payment_id: paymentId });
  if (!order) {
    return res.status(400).json({ message: "Order not found" });
  }

  // Verify transaction on Pi blockchain
  const response = await axios.get(txURL);
  const paymentIdOnBlock = response.data.memo;

  if (paymentIdOnBlock !== order.pi_payment_id) {
    return res.status(400).json({ message: "Payment ID doesn't match" });
  }

  // Mark order as paid and complete payment
  await db.orders.update(
    { pi_payment_id: paymentId },
    { txid, paid: true, completed_at: new Date() }
  );

  await piClient.post(`/v2/payments/${paymentId}/complete`, { txid });
  res.json({ message: `Handled incomplete payment ${paymentId}` });
});
```

## Database Schema (Adaptable to Any Database)

### User Table/Collection
```sql
-- SQL example
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    uid VARCHAR(255) UNIQUE NOT NULL,     -- Pi Network user ID
    username VARCHAR(255) NOT NULL,       -- Pi Network username
    roles JSON,                           -- User roles array
    access_token TEXT,                    -- Current Pi access token
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);
```

```python
# SQLAlchemy/Pydantic model example
class User(BaseModel):
    id: Optional[int] = None
    uid: str                    # Pi Network user ID (primary identifier)
    username: str               # Pi Network username
    roles: List[str] = []       # User roles
    access_token: str           # Current Pi access token
    created_at: datetime
    updated_at: datetime
```

### Orders Table/Collection
```sql
-- SQL example
CREATE TABLE orders (
    id SERIAL PRIMARY KEY,
    pi_payment_id VARCHAR(255) UNIQUE NOT NULL,  -- Pi payment identifier
    product_id VARCHAR(255) NOT NULL,            -- Product being purchased
    user_uid VARCHAR(255) NOT NULL,              -- User UID (foreign key)
    txid VARCHAR(255),                           -- Blockchain transaction ID
    amount DECIMAL(10,2) NOT NULL,               -- Payment amount
    paid BOOLEAN DEFAULT FALSE,                  -- Payment status
    cancelled BOOLEAN DEFAULT FALSE,             -- Cancellation status
    created_at TIMESTAMP DEFAULT NOW(),
    completed_at TIMESTAMP,
    FOREIGN KEY (user_uid) REFERENCES users(uid)
);
```

```python
# Pydantic model example
class Order(BaseModel):
    id: Optional[int] = None
    pi_payment_id: str          # Pi payment identifier
    product_id: str             # Product being purchased
    user_uid: str               # User UID
    txid: Optional[str] = None  # Blockchain transaction ID
    amount: float               # Payment amount
    paid: bool = False          # Payment status
    cancelled: bool = False     # Cancellation status
    created_at: datetime
    completed_at: Optional[datetime] = None
```

## Environment Configuration

### Required Environment Variables
```bash
# Pi Platform Integration (Required)
PI_API_KEY=your_pi_api_key_from_developer_portal
PLATFORM_API_URL=https://api.minepi.com

# Application Security (Required)
SESSION_SECRET=your_random_session_secret_string
JWT_SECRET=your_jwt_secret_if_using_jwt

# Database Configuration (Adapt to your database)
DATABASE_URL=your_database_connection_string
# Examples:
# PostgreSQL: postgresql://user:password@localhost:5432/dbname
# MongoDB: **********************************************
# SQLite: sqlite:///./app.db

# Application URLs
FRONTEND_URL=http://localhost:3000
BACKEND_URL=http://localhost:8000

# Pi SDK Configuration
SANDBOX_MODE=true  # Use sandbox for development, false for production
```

### Frontend Environment Variables (Framework Specific)
```bash
# Next.js
NEXT_PUBLIC_BACKEND_URL=http://localhost:8000
NEXT_PUBLIC_SANDBOX_SDK=true

# React (Create React App)
REACT_APP_BACKEND_URL=http://localhost:8000
REACT_APP_SANDBOX_SDK=true

# Vue.js
VUE_APP_BACKEND_URL=http://localhost:8000
VUE_APP_SANDBOX_SDK=true
```

## Security Considerations

### 1. Access Token Verification
- **Never trust frontend data**: Always verify access tokens on the backend
- **Use Pi Platform API**: The `/v2/me` endpoint is the source of truth
- **Handle token expiration**: Implement proper error handling for invalid tokens
- **Store tokens securely**: Never expose access tokens in frontend code

### 2. Payment Security
- **Server-side validation**: All payment logic must be validated on the backend
- **Blockchain verification**: Verify transactions using the Pi blockchain
- **Idempotency**: Handle duplicate payment requests gracefully
- **Amount validation**: Always verify payment amounts match your prices

### 3. Session/Authentication Security
- **Secure session storage**: Use secure session storage (database, Redis, etc.)
- **CORS configuration**: Properly configure CORS for your domain
- **Strong secrets**: Use strong, random session/JWT secrets
- **HTTPS only**: Always use HTTPS in production

### 4. API Security
- **Rate limiting**: Implement rate limiting on your API endpoints
- **Input validation**: Validate all input data
- **Error handling**: Don't expose sensitive information in error messages

## Implementation Checklist

### Prerequisites
- [ ] Register app on Pi Developer Portal (develop.pi in Pi Browser)
- [ ] Obtain Pi API key from developer portal
- [ ] Set up your preferred database
- [ ] Configure environment variables

### Frontend Setup (Any Framework)
- [ ] Include Pi SDK script in HTML
- [ ] Initialize Pi SDK with sandbox/production mode
- [ ] Implement authentication flow with Pi.authenticate()
- [ ] Implement payment callbacks (approval, completion, cancel, error)
- [ ] Configure HTTP client for API calls
- [ ] Handle incomplete payments during authentication

### Backend Setup (Any Framework)
- [ ] Set up your preferred web framework
- [ ] Configure database connection
- [ ] Implement user authentication endpoints
- [ ] Implement payment processing endpoints (approve, complete, cancel, incomplete)
- [ ] Set up Pi Platform API client
- [ ] Configure CORS properly
- [ ] Implement session/JWT authentication

### Database Setup (Any Database)
- [ ] Create users table/collection with uid, username, roles, access_token
- [ ] Create orders table/collection with payment tracking fields
- [ ] Set up proper indexes (uid, pi_payment_id)
- [ ] Configure database connection and migrations

### Testing
- [ ] Test authentication flow in Pi Sandbox
- [ ] Test payment approval process
- [ ] Test payment completion process
- [ ] Test incomplete payment handling
- [ ] Test payment cancellation
- [ ] Test error scenarios and edge cases
- [ ] Test with real Pi Browser (not just sandbox)
