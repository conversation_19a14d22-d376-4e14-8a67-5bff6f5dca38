# Software Requirements

## 1. Project Overview
   - Purpose and Scope: This is a blockchain-based project management tool built on the Pi Network, developed under the **Plover** brand. It enables teams to organize tasks, collaborate efficiently, and manage workflows with the added benefit of secure Pi-based payments.
   - Glossary of Terms and Acronyms: To be determined
   - References and Related Documents: README.md

## 2. Product Context
   - Product Perspective: This is a blockchain-based project management tool built on the Pi Network.
   - User Classes and Characteristics: To be determined
   - Operating Environment:
      - Frontend: Next.js, TypeScript, TailwindCSS
      - Backend: FastAPI (Python)
      - Database: PostgreSQL (Production), SQLite (Development)
      - Blockchain: Pi Network (for authentication & payments)
   - Constraints and Assumptions: To be determined
   - Dependencies:
      - Frontend: Next.js, TypeScript, TailwindCSS
      - Backend: FastAPI (Python)
      - Database: PostgreSQL (Production), SQLite (Development)
      - Blockchain: Pi Network (for authentication & payments)

## 3. Functional Requirements
   - User Features:
      - Task creation, assignment, and tracking
      - Team collaboration boards with categories and deadlines
      - Real-time updates and notifications
      - Subscription-based access with Pi cryptocurrency
      - Integrated Pi Authentication and Payment SDK
   - Business Rules:
      - Subscription-based access after the initial free trial.
      - No autosubscription; user confirmation is required for payments.
      - Data deletion after 60 days if the user doesn't confirm their subscription.
   - Authentication & Authorization:
      - Pi Authentication
   - External Interfaces:
      - Pi SDK for payments
   - Reports & Analytics: To be determined

## 4. Non-Functional Requirements
   - Performance: To be determined
   - Scalability: Dynamic plans & features; Admins can update pricing, introduce new features, or adjust existing ones over time.
   - Security: Secure Pi-based payments on the Pi Blockchain; No autosubscription.
   - Usability: To be determined
   - Maintainability: To be determined

## 5. User Stories & Use Cases
   - To be determined

## 6. Interface Requirements
   - User Interfaces: To be determined
   - API/External Interfaces:
      - Pi SDK: Secure payments on the Pi Blockchain
   - Hardware/Software Interfaces: To be determined

## 7. Data Requirements
   - To be determined

## 8. Open Questions & Decisions
   - To be determined