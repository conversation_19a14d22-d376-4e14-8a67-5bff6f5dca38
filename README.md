# Plover Project Management Tool

This is a blockchain-based project management tool built on the Pi Network, developed under the **Plover** brand.  
It enables teams to organize tasks, collaborate efficiently, and manage workflows with the added benefit of secure Pi-based payments.  
While the product name is still to be finalized, it will be released as one of the flagship services of **Plover**.


## Features

- Task creation, assignment, and tracking
- Team collaboration boards with categories and deadlines
- Real-time updates and notifications
- Subscription-based access with Pi cryptocurrency
- Integrated Pi Authentication and Payment SDK

## Tech Stack

- **Frontend**: Next.js, TypeScript, TailwindCSS
- **Backend**: FastAPI (Python)
- **Database**: PostgreSQL (Production), SQLite (Development)
- **Blockchain**: Pi Network (for authentication & payments)

## Deployment

- Backend: FastAPI hosted on server/container
- Database: PostgreSQL (cloud-hosted)
- Frontend: Deployed via Vercel/Netlify with Pi Browser compatibility
- Payments & Auth: Pi SDK Integration

---

## Pi Payments Process Documentation

This document outlines the three-step process for integrating Pi Payments into an application. The process involves interactions between the App Frontend, App Backend, and the Pi Platform.

### 1. Introduction

The Pi Payments process is a secure and streamlined way for applications to receive payments in Pi cryptocurrency. It involves a series of steps to ensure that all parties (the user, the application, and the Pi Platform) are aware of the transaction status and can act accordingly.

The main components are:

- **App Frontend**: The user-facing application on a mobile device.
- **App Backend**: The server-side logic for the application.
- **Pi Platform**: The official Pi Network API and blockchain.

---

### 2. Step 1: Create Payment

This is the initial step where the application requests a payment.

1. **App Frontend → Pi Platform**: The App Frontend initiates a request to the Pi Platform. This request states that a specific user should pay a certain amount of Pi (X Pi).  

   - **Function Name**: `SDK: createPayment()`

---

### 3. Step 2: Approve Payment

After the payment is created, the Pi Platform provides a reference ID, and the application's backend is notified.

1. **Pi Platform → App Frontend**: The Pi Platform responds with a `PaymentID` for reference. The App Frontend is instructed to inform its backend to confirm its awareness of the payment situation.  

   - **Callback**: `onReadyForServerApproval()`  

2. **App Frontend → App Backend**: The App Frontend passes the `PaymentID` to the App Backend.  

3. **App Backend → Pi Platform**: The App Backend sends a POST request to the Pi Platform's API to confirm approval of the payment.  

   - **API Endpoint**: `POST /payments/{PaymentID}/approve`

---

### 4. Step 3: Complete Payment

This final step confirms the payment transaction and informs the application to deliver the goods or services.

1. **Pi Platform → App Backend**: Once the user completes the transaction (`TX`) on the Pi blockchain, the Pi Platform notifies the App Backend. The notification includes the transaction ID (`TXid`).  

   - **Callback**: `onReadyForServerCompletion()`  

   > Note: This notification confirms that the transaction is completed. The App Backend should then take action to deliver the goods or services.

2. **App Backend → App Frontend**: The App Backend passes the `TXid` back to the App Frontend to confirm the payment completion.  

3. **App Backend → Pi Platform**: The App Backend sends a final POST request to the Pi Platform to complete the payment on the platform side.  

   - **API Endpoint**: `POST /payment/{TXid}/complete`


## Payment & Plans

The app integrates with the **Pi SDK** for secure payments on the **Pi Blockchain**.  
Users can subscribe to different plans based on their needs. After the initial free trial, users are required to subscribe to a plan to continue using the service. there's no autosubscription due to security reasons, the user has to confirm their subscription, and that's when they pay using the Pi SDK. when the user doesn't confirm their subscription, they are not charged but their access is revoked and their data is deleted after 60 days.

### Available Plans
- **Free Trial**: 1 month free access  
- **Basic Plan**: 5 Pi / month  
- **Standard Plan**: 15 Pi / month  
- **Premium Plan**: 25 Pi / month  

### Key Note
- **Dynamic Plans & Features**: The subscription plans and features are **not static**.  
  Admins can update pricing, introduce new features, or adjust existing ones over time.  
  This ensures flexibility and scalability for both the users and the business.  



