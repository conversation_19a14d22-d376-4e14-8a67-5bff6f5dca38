# System Architecture Design Document

## 1. Executive Summary

The Plover Project Management Tool is a blockchain-based platform designed to facilitate task management, team collaboration, and secure Pi-based payments. This document outlines the system architecture, technology stack, and key design decisions for the project.

## 2. Requirements Analysis

### 2.1 Functional Requirements

- Task creation, assignment, and tracking
- Team collaboration boards with categories and deadlines
- Real-time updates and notifications
- Subscription-based access with Pi cryptocurrency
- Integrated Pi Authentication and Payment SDK

### 2.2 Non-Functional Requirements

- Performance: The system should provide a responsive user experience with minimal latency.
- Scalability: The system should be able to handle a growing number of users and tasks.
- Security: The system should ensure secure Pi-based payments and protect user data.
- Usability: The system should be easy to use and navigate.
- Maintainability: The system should be designed for easy maintenance and updates.

### 2.3 Constraints and Assumptions

- The system will be built on the Pi Network blockchain.
- Users will access the system through a web browser or mobile app.
- The system will integrate with the Pi SDK for payments.

## 3. Architecture Overview

### 3.1 Selected Architecture Style(s)

- **RESTful:** The services will communicate with each other using RESTful APIs, which are stateless and easy to integrate.
- **Event-Driven:** The system will use an event-driven architecture to enable real-time updates and notifications.

### 3.2 High-Level Architecture Diagram

The system architecture consists of the following components:

- **Frontend:** A Next.js application that provides the user interface.
- **Task Management Service:** A FastAPI service that manages tasks, assignments, and deadlines.
- **Collaboration Service:** A FastAPI service that manages team collaboration boards.
- **Payment Service:** A FastAPI service that integrates with the Pi SDK for secure payments.
- **Notification Service:** A FastAPI service that sends real-time updates and notifications to users.
- **Database:** A PostgreSQL database that stores the system data.

### 3.3 Core Components

- **Frontend:** Provides the user interface for the system.
- **Task Management Service:** Manages tasks, assignments, and deadlines.
- **Collaboration Service:** Manages team collaboration boards.
- **Payment Service:** Integrates with the Pi SDK for secure payments.
- **Notification Service:** Sends real-time updates and notifications to users.
- **Database:** Stores the system data.

## 4. Technology Stack

### 4.1 Backend Framework

- FastAPI (Python): A modern, high-performance web framework for building APIs.

### 4.2 Database Technology

- PostgreSQL(Production), SQLite(Development): A robust, open-source relational database.

### 4.3 Additional Technologies

- Next.js: A React framework for building web applications.
- TypeScript: A superset of JavaScript that adds static typing.
- TailwindCSS: A utility-first CSS framework.
- Pi Network: A cryptocurrency and blockchain platform.
- Pi SDK: A software development kit for integrating with the Pi Network.
- Docker/Kubernetes: Containerization and orchestration tools for deployment.

## 5. Detailed Design

### 5.1 System Components

- **Frontend:** The frontend will be a Next.js application that provides a user-friendly interface for managing tasks, collaborating with team members, and making payments.
- **Task Management Service:** The task management service will be responsible for creating, assigning, and tracking tasks. It will provide APIs for managing tasks, assignments, and deadlines.
- **Collaboration Service:** The collaboration service will be responsible for managing team collaboration boards. It will provide APIs for creating, updating, and deleting boards, as well as for adding and removing members.
- **Payment Service:** The payment service will be responsible for integrating with the Pi SDK for secure payments. It will provide APIs for processing payments, managing subscriptions, and handling refunds.
- **Notification Service:** The notification service will be responsible for sending real-time updates and notifications to users. It will use a message queue to send notifications asynchronously.
- **Database:** The database will store all the system data, including tasks, assignments, deadlines, users, payments, and notifications.
- **Message Queue:** The message queue will enable event-driven communication between services.

### 5.2 Data Flow

1.  A user creates a task in the frontend.
2.  The frontend sends a request to the task management service to create the task.
3.  The task management service creates the task in the database.
4.  The task management service sends an event to the message queue to notify other services about the new task.
5.  The notification service receives the event and sends a notification to the user.

### 5.3 API Design

-   **Task Management Service:**
    -   `POST /tasks`: Create a new task.
    -   `GET /tasks/{id}`: Get a task by ID.
    -   `PUT /tasks/{id}`: Update a task.
    -   `PATCH /tasks/{id}`: Update a task partially.
    -   `GET /tasks`: Get all tasks.
    -   `GET /tasks/assigned`: Get all tasks assigned to a user.
    -   `DELETE /tasks/{id}`: Delete a task.
-   **Collaboration Service:**
    -   `POST /boards`: Create a new board.
    -   `GET /boards/{id}`: Get a board by ID.
    -   `PUT /boards/{id}`: Update a board.
    -   `PATCH /boards/{id}`: Update a board partially.
    -   `GET /boards`: Get all boards.
    -   `DELETE /boards/{id}`: Delete a board.
-   **Payment Service:**[check the pi authentication and payment markdown file]

### 5.4 Data Model

-   **Task:**
    -   `id`: Unique identifier.
    -   `title`: Task title.
    -   `description`: Task description.
    -   `assignee`: User ID of the assignee.
    -   `deadline`: Task deadline.
    -   `status`: Task status (e.g., open, in progress, completed).
-   **Board:**
    -   `id`: Unique identifier.
    -   `name`: Board name.
    -   `description`: Board description.
    -   `members`: List of user IDs.
-   **Payment:**
    -   `id`: Unique identifier.
    -   `user_id`: User ID of the payer.
    -   `amount`: Payment amount.
    -   `status`: Payment status (e.g., pending, completed, failed).
-   **Subscription:**
    -   `id`: Unique identifier.
    -   `user_id`: User ID of the subscriber.
    -   `plan_id`: Plan ID of the subscription.
    -   `start_date`: Subscription start date.
    -   `end_date`: Subscription end date.

## 6. Non-Functional Implementation


### 6.1 Security Architecture

-   **Authentication:** Users will be authenticated using Pi Authentication.
-   **Authorization:** Access to resources will be controlled using role-based access control (RBAC).
-   **Data Protection:** Sensitive data will be encrypted at rest and in transit.
-   **Network Security:** Firewalls and intrusion detection systems will be used to protect the network.

### 6.2 Performance Optimization

-   **Caching:** Caching will be used to reduce database load and improve response times.
-   **Asynchronous Processing:** Asynchronous processing will be used to handle long-running tasks.
-   **Database Optimization:** The database will be optimized for performance by using indexes and query optimization techniques.

### 6.4 Reliability & Resilience

-   **Disaster Recovery:** A disaster recovery plan will be in place to ensure that the system can be recovered in the event of a disaster.

### 6.5 Observability

-   **Logging:** All services will log events to a central logging system.
-   **Monitoring:** The system will be monitored using a monitoring tool (e.g., Prometheus or Grafana).
-   **Alerting:** Alerts will be generated when the system is experiencing problems.