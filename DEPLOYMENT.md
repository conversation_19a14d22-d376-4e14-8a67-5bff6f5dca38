# Plover Project Management Tool - Deployment Guide

## Table of Contents
- [Prerequisites](#prerequisites)
- [Local Development Setup](#local-development-setup)
- [Production Deployment](#production-deployment)
- [Environment Configuration](#environment-configuration)
- [Docker Deployment](#docker-deployment)
- [Nginx Configuration](#nginx-configuration)
- [SSL/HTTPS Setup](#sslhttps-setup)
- [Troubleshooting](#troubleshooting)

## Prerequisites

### System Requirements
- **Node.js**: 18.x or higher
- **Python**: 3.11 or higher
- **Docker**: 20.x or higher
- **Docker Compose**: 2.x or higher
- **PostgreSQL**: 13.x or higher (for production)
- **Redis**: 6.x or higher
- **Nginx**: 1.20 or higher (for production)

### Required Accounts
- **Pi Network Developer Account**: For Pi Network integration
- **Domain Name**: For production deployment (optional but recommended)

## Local Development Setup

### 1. Clone the Repository
```bash
git clone <your-repository-url>
cd plover-pm-tool
```

### 2. Backend Setup
```bash
cd backend

# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Copy environment file
cp .env.example .env

# Edit .env file with your configuration
nano .env
```

### 3. Frontend Setup
```bash
cd ../frontend

# Install dependencies
npm install

# Copy environment file
cp .env.example .env.local

# Edit .env.local file with your configuration
nano .env.local
```

### 4. Database Setup
```bash
cd ../backend

# Start PostgreSQL and Redis with Docker
docker compose up -d postgres redis

# Run database migrations
alembic upgrade head
```

### 5. Start Development Servers
```bash
# Terminal 1: Backend
cd backend
source venv/bin/activate
uvicorn main:app --reload --host 0.0.0.0 --port 8000

# Terminal 2: Frontend
cd frontend
npm run dev
```

Visit `http://localhost:3000` to access the application.

## Production Deployment

### Option 1: Docker Deployment (Recommended)

#### 1. Server Preparation
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# Logout and login again for group changes to take effect
```

#### 2. Application Deployment
```bash
# Clone repository
git clone <your-repository-url>
cd plover-pm-tool

# Create production environment files
cp backend/.env.example backend/.env.production
cp frontend/.env.example frontend/.env.production

# Edit environment files (see Environment Configuration section)
nano backend/.env.production
nano frontend/.env.production

# Build and start all services
docker compose -f docker-compose.prod.yml up -d --build
```

### Option 2: Manual Deployment

#### 1. Backend Deployment
```bash
# Install Python and dependencies
sudo apt install python3.11 python3.11-venv python3-pip postgresql-client -y

# Create application directory
sudo mkdir -p /opt/plover
sudo chown $USER:$USER /opt/plover
cd /opt/plover

# Clone and setup backend
git clone <your-repository-url> .
cd backend
python3.11 -m venv venv
source venv/bin/activate
pip install -r requirements.txt

# Setup systemd service
sudo cp deployment/plover-backend.service /etc/systemd/system/
sudo systemctl daemon-reload
sudo systemctl enable plover-backend
sudo systemctl start plover-backend
```

#### 2. Frontend Deployment
```bash
cd /opt/plover/frontend

# Install Node.js
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Build frontend
npm install
npm run build

# Setup systemd service
sudo cp deployment/plover-frontend.service /etc/systemd/system/
sudo systemctl daemon-reload
sudo systemctl enable plover-frontend
sudo systemctl start plover-frontend
```

## Environment Configuration

### Backend (.env.production)
```bash
# Application Configuration
APP_NAME="Plover Project Management Tool"
APP_VERSION="1.0.0"
ENVIRONMENT="production"
DEBUG=false
HOST="0.0.0.0"
PORT=8000

# Security
SECRET_KEY="your-super-secret-key-min-32-chars-change-this"
ACCESS_TOKEN_EXPIRE_MINUTES=30
ALGORITHM="HS256"

# CORS Configuration
ALLOWED_ORIGINS="https://yourdomain.com,https://www.yourdomain.com"
ALLOWED_HOSTS="yourdomain.com,www.yourdomain.com"

# Database Configuration (PostgreSQL)
DATABASE_URL="postgresql+asyncpg://plover_user:secure_password@localhost:5432/plover_pm"
DATABASE_ECHO=false

# Pi Network Integration
PI_API_KEY="your-production-pi-api-key"
PI_PLATFORM_API_URL="https://api.minepi.com"
PI_SANDBOX_MODE=false

# Frontend Configuration
FRONTEND_URL="https://yourdomain.com"

# Logging
LOG_LEVEL="INFO"
```

### Frontend (.env.production)
```bash
# Backend API Configuration
NEXT_PUBLIC_API_URL=https://yourdomain.com/api
NEXT_PUBLIC_BACKEND_URL=https://yourdomain.com/api

# Pi Network Configuration
NEXT_PUBLIC_PI_SANDBOX=false
NEXT_PUBLIC_PI_API_KEY=your-production-pi-api-key

# Application Configuration
NEXT_PUBLIC_APP_NAME="Plover Project Management Tool"
NEXT_PUBLIC_APP_VERSION="1.0.0"
```

## Docker Deployment

### Production Docker Compose
Create `docker-compose.prod.yml`:
```yaml
version: '3.8'

services:
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: plover_pm
      POSTGRES_USER: plover_user
      POSTGRES_PASSWORD: secure_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - plover-network

  redis:
    image: redis:7-alpine
    volumes:
      - redis_data:/data
    networks:
      - plover-network

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.prod
    environment:
      - DATABASE_URL=postgresql+asyncpg://plover_user:secure_password@postgres:5432/plover_pm
    depends_on:
      - postgres
      - redis
    networks:
      - plover-network

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.prod
    depends_on:
      - backend
    networks:
      - plover-network

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - frontend
      - backend
    networks:
      - plover-network

volumes:
  postgres_data:
  redis_data:

networks:
  plover-network:
    driver: bridge
```

## Nginx Configuration

### Basic Nginx Configuration
Create `nginx/nginx.conf`:
```nginx
events {
    worker_connections 1024;
}

http {
    upstream backend {
        server backend:8000;
    }

    upstream frontend {
        server frontend:3000;
    }

    server {
        listen 80;
        server_name yourdomain.com www.yourdomain.com;

        # Redirect HTTP to HTTPS
        return 301 https://$server_name$request_uri;
    }

    server {
        listen 443 ssl http2;
        server_name yourdomain.com www.yourdomain.com;

        # SSL Configuration
        ssl_certificate /etc/nginx/ssl/fullchain.pem;
        ssl_certificate_key /etc/nginx/ssl/privkey.pem;
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
        ssl_prefer_server_ciphers off;

        # Security Headers
        add_header X-Frame-Options DENY;
        add_header X-Content-Type-Options nosniff;
        add_header X-XSS-Protection "1; mode=block";
        add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

        # API Routes
        location /api/ {
            proxy_pass http://backend/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Frontend Routes
        location / {
            proxy_pass http://frontend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }
}
```

## SSL/HTTPS Setup

### Option 1: Let's Encrypt (Free SSL)
```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx -y

# Obtain SSL certificate
sudo certbot --nginx -d yourdomain.com -d www.yourdomain.com

# Auto-renewal setup
sudo crontab -e
# Add this line:
# 0 12 * * * /usr/bin/certbot renew --quiet
```

### Option 2: Manual SSL Certificate
```bash
# Create SSL directory
mkdir -p ssl

# Copy your SSL certificates
cp your-certificate.crt ssl/fullchain.pem
cp your-private-key.key ssl/privkey.pem

# Set proper permissions
chmod 600 ssl/privkey.pem
chmod 644 ssl/fullchain.pem
```

## Database Migration

### Production Database Setup
```bash
# Connect to your server
ssh user@your-server

# Create database and user
sudo -u postgres psql
CREATE DATABASE plover_pm;
CREATE USER plover_user WITH PASSWORD 'secure_password';
GRANT ALL PRIVILEGES ON DATABASE plover_pm TO plover_user;
\q

# Run migrations
cd /opt/plover/backend
source venv/bin/activate
alembic upgrade head
```

## Monitoring and Maintenance

### Log Management
```bash
# View application logs
docker compose logs -f backend
docker compose logs -f frontend

# System service logs
sudo journalctl -u plover-backend -f
sudo journalctl -u plover-frontend -f
```

### Backup Strategy
```bash
# Database backup script
#!/bin/bash
BACKUP_DIR="/opt/backups"
DATE=$(date +%Y%m%d_%H%M%S)
pg_dump -h localhost -U plover_user plover_pm > $BACKUP_DIR/plover_pm_$DATE.sql
```

## Troubleshooting

### Common Issues

#### CORS Errors
- Ensure `ALLOWED_ORIGINS` in backend `.env` includes your domain
- Check that frontend `NEXT_PUBLIC_API_URL` points to correct backend URL
- Verify Nginx proxy configuration

#### Database Connection Issues
- Check database credentials in `.env` file
- Ensure PostgreSQL is running: `sudo systemctl status postgresql`
- Verify network connectivity between containers

#### Pi Network Integration Issues
- Verify `PI_API_KEY` is correct for your environment
- Check `PI_SANDBOX_MODE` setting matches your Pi Network app configuration
- Ensure your domain is registered in Pi Network Developer Portal

#### SSL Certificate Issues
- Check certificate expiration: `openssl x509 -in ssl/fullchain.pem -text -noout`
- Verify certificate chain is complete
- Ensure proper file permissions on certificate files

### Performance Optimization

#### Backend Optimization
```bash
# Increase worker processes
uvicorn main:app --workers 4 --host 0.0.0.0 --port 8000
```

#### Frontend Optimization
```bash
# Build with optimization
npm run build
npm run start
```

#### Database Optimization
```sql
-- Create indexes for better performance
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_projects_created_at ON projects(created_at);
CREATE INDEX idx_tasks_status ON tasks(status);
```

## Security Checklist

- [ ] Change default passwords and secret keys
- [ ] Enable SSL/HTTPS
- [ ] Configure firewall (UFW)
- [ ] Set up regular backups
- [ ] Enable fail2ban for SSH protection
- [ ] Update system packages regularly
- [ ] Monitor application logs
- [ ] Use strong database passwords
- [ ] Restrict database access to application only
- [ ] Enable CORS only for trusted domains

## Support

For issues and support:
1. Check the troubleshooting section above
2. Review application logs
3. Check GitHub issues
4. Contact the development team

---

**Last Updated**: $(date)
**Version**: 1.0.0
